import pandas as pd
import numpy as np
import pickle
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder

# Sample dataset with sleep hours, sleep quality, and stress level
data = {
    "sleep_hours": [4, 5, 6, 7, 8, 3, 5, 7, 9, 4, 6, 8, 2, 7, 5],
    "sleep_quality": [2, 3, 4, 5, 5, 1, 2, 4, 5, 2, 3, 5, 1, 4, 3],
    "stress_level": ["High", "High", "Medium", "Medium", "Low", "High", "High", "Medium", "Low", "High", "Medium", "Low", "High", "Medium", "Medium"]
}

df = pd.DataFrame(data)

# Encode stress levels as numerical values
label_encoder = LabelEncoder()
df["stress_level"] = label_encoder.fit_transform(df["stress_level"])

# Split data into training and testing sets
X = df[["sleep_hours", "sleep_quality"]]
y = df["stress_level"]
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Train a RandomForestClassifier model
model = RandomForestClassifier(n_estimators=100, random_state=42)
model.fit(X_train, y_train)

# Save the trained model to 'stress_model.pkl'
with open("stress_model.pkl", "wb") as f:
    pickle.dump(model, f)

print("Model trained and saved as 'stress_model.pkl'.")
