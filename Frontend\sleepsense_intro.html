<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sleep Stress Analysis</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700&family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            overflow: hidden;
            font-family: 'Poppins', sans-serif;
        }

        .intro-container {
            position: fixed;
            width: 100%;
            height: 100vh;
            background: linear-gradient(135deg, #0f172a, #1e293b, #334155);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            text-align: center;
            z-index: 9999;
        }

        .intro-logo {
            font-size: 3.5rem;
            font-weight: 700;
            opacity: 0;
            animation: fadeIn 2s ease-in-out forwards;
            text-shadow: 0 0 15px rgba(56, 189, 248, 0.7);
            margin-bottom: 15px;
            background: linear-gradient(90deg, #38bdf8, #818cf8, #c084fc);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            letter-spacing: 1px;
            font-family: 'Montserrat', sans-serif;
        }

        .intro-tagline {
            font-size: 1.3rem;
            margin-top: 10px;
            opacity: 0;
            animation: fadeIn 2s ease-in-out 1s forwards;
            color: #e2e8f0;
            font-weight: 300;
            letter-spacing: 0.5px;
        }

        .loading-bar-container {
            width: 300px;
            height: 6px;
            background: rgba(255, 255, 255, 0.1);
            margin-top: 40px;
            border-radius: 10px;
            overflow: hidden;
            position: relative;
            box-shadow: 0 0 10px rgba(56, 189, 248, 0.3);
        }

        .loading-bar {
            position: absolute;
            height: 100%;
            width: 0%;
            background: linear-gradient(90deg, #38bdf8, #818cf8, #c084fc);
            border-radius: 10px;
            animation: loading 3s ease-in-out forwards;
            box-shadow: 0 0 15px rgba(56, 189, 248, 0.8);
        }

        .brain-waves {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.15;
            z-index: -1;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1000 1000"><path d="M0,500 Q250,400 500,500 T1000,500" stroke="white" fill="none" stroke-width="2" /></svg>') repeat-x;
            background-size: 100% 100%;
            animation: wave 15s linear infinite;
        }

        .particles-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0);
            border-radius: 50%;
            pointer-events: none;
        }

        .text-box {
            background: rgba(15, 23, 42, 0.6);
            padding: 30px 50px;
            border-radius: 16px;
            backdrop-filter: blur(10px);
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3),
                        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
            margin-bottom: 30px;
            transform: translateY(20px);
            opacity: 0;
            animation: slideUp 1s ease-out 1.5s forwards;
            max-width: 600px;
            width: 90%;
        }

        #intro-text {
            font-size: 2rem;
            margin-bottom: 15px;
            letter-spacing: 1px;
            font-weight: 600;
            background: linear-gradient(90deg, #38bdf8, #818cf8);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        .text-box p {
            font-size: 1.1rem;
            line-height: 1.6;
            color: #e2e8f0;
            font-weight: 300;
        }

        .brain-icon {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
            opacity: 0;
            animation: fadeIn 2s ease-in-out 0.5s forwards,
                       pulse 3s infinite ease-in-out;
            filter: drop-shadow(0 0 10px rgba(56, 189, 248, 0.7));
        }

        .status-text {
            font-size: 0.9rem;
            color: #94a3b8;
            margin-top: 15px;
            letter-spacing: 1px;
            opacity: 0;
            animation: blink 2s infinite, fadeIn 1s ease-in-out 2s forwards;
        }

        @keyframes wave {
            0% {
                background-position: 0 0;
            }
            100% {
                background-position: 1000px 0;
            }
        }

        @keyframes float {
            0% {
                transform: translateY(0) translateX(0) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 0.8;
            }
            90% {
                opacity: 0.8;
            }
            100% {
                transform: translateY(-100vh) translateX(100px) rotate(360deg);
                opacity: 0;
            }
        }

        @keyframes loading {
            0% {
                width: 0%;
            }
            20% {
                width: 20%;
            }
            50% {
                width: 60%;
            }
            80% {
                width: 85%;
            }
            100% {
                width: 100%;
            }
        }

        @keyframes fadeIn {
            0% {
                opacity: 0;
                transform: scale(0.95);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes slideUp {
            0% {
                transform: translateY(20px);
                opacity: 0;
            }
            100% {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes slideUpExit {
            0% {
                transform: translateY(0);
            }
            100% {
                transform: translateY(-100%);
                opacity: 0;
            }
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }

        @keyframes blink {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.7;
            }
        }

        .hide-intro {
            animation: slideUpExit 1s ease-in-out forwards;
        }

        .main-content {
            display: none;
        }

        .neuron {
            position: absolute;
            width: 3px;
            height: 3px;
            background: rgba(56, 189, 248, 0.7);
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(56, 189, 248, 0.5);
        }

        .synapse {
            position: absolute;
            height: 1px;
            transform-origin: left center;
            background: linear-gradient(90deg, rgba(56, 189, 248, 0.7), rgba(56, 189, 248, 0));
        }

        .pulse-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(56, 189, 248, 0.1);
            transform: translate(-50%, -50%);
            animation: pulseCircle 3s infinite;
        }

        @keyframes pulseCircle {
            0% {
                width: 0;
                height: 0;
                opacity: 0.8;
            }
            100% {
                width: 200px;
                height: 200px;
                opacity: 0;
            }
        }
    </style>
</head>

<body>
    <div class="intro-container">
        <div class="particles-container" id="particles"></div>
        <div class="brain-waves"></div>
        <div id="neural-network"></div>

        <svg class="brain-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
            <path d="M9.5 2a2.5 2.5 0 1 1 0 5 2.5 2.5 0 0 1 0-5Z" stroke="#38bdf8"/>
            <path d="M14.5 2a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5Z" stroke="#818cf8"/>
            <path d="M3 10.5c0-1.1.9-2 2-2h14a2 2 0 0 1 2 2v1a2 2 0 0 1-2 2h-4l-4 2v-2H7a2 2 0 0 1-2-2v-1Z" stroke="#c084fc"/>
            <path d="M12 22c-1.5-1-2-3.5-2-6h4c0 2.5-.5 5-2 6Z" stroke="#38bdf8"/>
            <path d="M19 8.5V10" stroke="#818cf8"/>
            <path d="M5 8.5V10" stroke="#c084fc"/>
        </svg>

        <div class="intro-logo">SleepSense AI</div>
        <div class="intro-tagline">Advanced Stress Detection Through Sleep Analysis</div>

        <div class="text-box">
            <h1 id="intro-text">Analyzing Sleep Patterns</h1>
            <p>Our advanced AI is processing your sleep data to identify stress patterns and provide personalized insights for better mental wellbeing.</p>
        </div>

        <div class="loading-bar-container">
            <div class="loading-bar"></div>
        </div>
        <div class="status-text">INITIALIZING NEURAL NETWORK</div>
    </div>

    <script>
        /**
         * SleepSense AI Intro Page
         */
        document.addEventListener('DOMContentLoaded', () => {
            console.log('✅ Intro page script loaded');

            // Create background particles
            createParticles();

            // Create neural network visualization
            createNeuralNetwork();

            // Initialize intro page functionality
            initIntroPage();
        });

        // Intro page functionality
        function initIntroPage() {
            setTimeout(() => {
                document.querySelector(".intro-container").classList.add("hide-intro");
                setTimeout(() => {
                    document.querySelector(".intro-container").remove();
                    if (document.querySelector(".main-content")) {
                        document.querySelector(".main-content").style.display = "block";
                    }
                }, 1000);
            }, 3000); // 3 seconds delay

            setTimeout(function () {
                window.location.href = "sleepsense_home.html"; // Redirect to home page after intro
            }, 4000); // 4 seconds total

            // Update status text periodically
            const statusTexts = [
                "INITIALIZING NEURAL NETWORK",
                "PROCESSING SLEEP DATA",
                "ANALYZING BRAINWAVE PATTERNS",
                "DETECTING STRESS MARKERS",
                "GENERATING INSIGHTS",
                "FINALIZING ANALYSIS"
            ];

            let statusIndex = 0;
            const statusElement = document.querySelector('.status-text');

            setInterval(() => {
                statusIndex = (statusIndex + 1) % statusTexts.length;
                statusElement.textContent = statusTexts[statusIndex];
            }, 3000);
        }

        // Create background particles
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            if (!particlesContainer) return;

            // Clear existing particles
            particlesContainer.innerHTML = '';

            const colors = [
                'rgba(56, 189, 248, 0.6)',  // Blue
                'rgba(129, 140, 248, 0.6)', // Purple
                'rgba(192, 132, 252, 0.6)'  // Pink
            ];

            // Create 30 particles (reduced for better performance)
            for (let i = 0; i < 30; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';

                // Random size between 2px and 6px
                const size = Math.random() * 4 + 2;
                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;

                // Random position
                const posX = Math.random() * 100;
                const posY = Math.random() * 100;
                particle.style.left = `${posX}%`;
                particle.style.top = `${posY}%`;

                // Random color
                const colorIndex = Math.floor(Math.random() * colors.length);
                particle.style.backgroundColor = colors[colorIndex];
                particle.style.boxShadow = `0 0 ${size * 2}px ${colors[colorIndex]}`;

                // Random animation duration between 15s and 30s
                const duration = Math.random() * 15 + 15;
                particle.style.animation = `float ${duration}s ease-in-out infinite`;
                particle.style.animationDelay = `-${Math.random() * duration}s`;

                particlesContainer.appendChild(particle);
            }
        }

        // Create neural network visualization
        function createNeuralNetwork() {
            const neuralNetwork = document.getElementById('neural-network');
            if (!neuralNetwork) return;

            // Clear existing network
            neuralNetwork.innerHTML = '';

            const neurons = [];
            const numNeurons = 15;

            // Create neurons
            for (let i = 0; i < numNeurons; i++) {
                const neuron = document.createElement('div');
                neuron.classList.add('neuron');

                // Position randomly but more towards the center
                const x = 30 + Math.random() * 40; // 30% to 70% of screen width
                const y = 30 + Math.random() * 40; // 30% to 70% of screen height

                neuron.style.left = x + 'vw';
                neuron.style.top = y + 'vh';

                neurons.push({ element: neuron, x, y });
                neuralNetwork.appendChild(neuron);
            }

            // Create synapses (connections between neurons)
            for (let i = 0; i < neurons.length; i++) {
                const neuron = neurons[i];

                // Connect to 2-4 other random neurons
                const connections = Math.floor(Math.random() * 3) + 2;

                for (let j = 0; j < connections; j++) {
                    // Select a random target neuron that's not the current one
                    let targetIndex;
                    do {
                        targetIndex = Math.floor(Math.random() * neurons.length);
                    } while (targetIndex === i);

                    const target = neurons[targetIndex];

                    // Calculate distance and angle
                    const dx = target.x - neuron.x;
                    const dy = target.y - neuron.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    const angle = Math.atan2(dy, dx) * 180 / Math.PI;

                    // Create synapse
                    const synapse = document.createElement('div');
                    synapse.classList.add('synapse');
                    synapse.style.left = neuron.x + 'vw';
                    synapse.style.top = neuron.y + 'vh';
                    synapse.style.width = distance + 'vw';
                    synapse.style.transform = `rotate(${angle}deg)`;

                    // Add pulse animation along synapse
                    setInterval(() => {
                        const pulse = document.createElement('div');
                        pulse.classList.add('pulse-circle');
                        pulse.style.left = (Math.random() * distance) + 'vw';
                        pulse.style.top = '0';
                        synapse.appendChild(pulse);

                        // Remove pulse after animation completes
                        setTimeout(() => {
                            pulse.remove();
                        }, 3000);
                    }, Math.random() * 5000 + 3000);

                    neuralNetwork.appendChild(synapse);
                }
            }
        }
    </script>
</body>

</html>