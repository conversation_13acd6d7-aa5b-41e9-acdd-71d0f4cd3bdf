<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SleepSense AI | Admin Login</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700&family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-dark: #0f172a;
            --primary-medium: #1e293b;
            --primary-light: #334155;
            --accent-blue: #38bdf8;
            --accent-purple: #818cf8;
            --accent-pink: #c084fc;
            --text-light: #e2e8f0;
            --text-muted: #94a3b8;
            --glass-bg: rgba(15, 23, 42, 0.7);
            --glass-border: rgba(255, 255, 255, 0.1);
            --glass-shadow: rgba(0, 0, 0, 0.3);
            --transition-normal: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-medium), var(--primary-light));
            color: var(--text-light);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        /* Background Effects */
        .particles-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            border-radius: 50%;
            pointer-events: none;
        }

        .brain-waves {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.1;
            z-index: -1;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1000 1000"><path d="M0,500 Q250,400 500,500 T1000,500" stroke="white" fill="none" stroke-width="2" /></svg>') repeat-x;
            background-size: 100% 100%;
            animation: wave 15s linear infinite;
            pointer-events: none;
        }

        @keyframes wave {
            0% {
                background-position: 0 0;
            }
            100% {
                background-position: 1000px 0;
            }
        }

        .login-container {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            padding: 2.5rem;
            border-radius: 16px;
            box-shadow: 0 20px 50px var(--glass-shadow);
            border: 1px solid var(--glass-border);
            width: 90%;
            max-width: 400px;
            text-align: center;
            position: relative;
            z-index: 1;
            animation: fadeIn 1s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1.5rem;
            filter: drop-shadow(0 0 10px rgba(56, 189, 248, 0.5));
        }

        h2 {
            font-family: 'Montserrat', sans-serif;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            background: linear-gradient(90deg, var(--accent-pink), var(--accent-purple));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        input {
            width: 100%;
            padding: 1rem;
            margin-bottom: 1.2rem;
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-light);
            font-family: 'Poppins', sans-serif;
            font-size: 1rem;
            transition: var(--transition-normal);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1) inset;
            text-align: center;
        }

        input:focus {
            outline: none;
            border-color: var(--accent-pink);
            box-shadow: 0 0 0 2px rgba(192, 132, 252, 0.3), 0 4px 10px rgba(0, 0, 0, 0.1) inset;
            background: rgba(255, 255, 255, 0.08);
        }

        input::placeholder {
            color: var(--text-muted);
        }

        button {
            width: 100%;
            padding: 1rem;
            border: none;
            border-radius: 10px;
            background: linear-gradient(90deg, var(--accent-pink), var(--accent-purple));
            color: white;
            font-family: 'Poppins', sans-serif;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            box-shadow: 0 4px 15px rgba(192, 132, 252, 0.3);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, var(--accent-pink), var(--accent-purple));
            z-index: -1;
            transition: var(--transition-normal);
        }

        button:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 20px rgba(192, 132, 252, 0.5);
        }

        button:hover::before {
            transform: scale(1.1);
            opacity: 0.9;
        }

        p {
            margin-top: 1.5rem;
            color: var(--text-muted);
            font-size: 0.9rem;
        }

        a {
            color: var(--accent-blue);
            text-decoration: none;
            transition: var(--transition-normal);
            position: relative;
        }

        a::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 1px;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
            transition: var(--transition-normal);
        }

        a:hover {
            color: var(--accent-purple);
        }

        a:hover::after {
            width: 100%;
        }

        .admin-badge {
            position: absolute;
            top: -15px;
            right: -15px;
            background: linear-gradient(90deg, var(--accent-pink), var(--accent-purple));
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 30px;
            font-size: 0.8rem;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(192, 132, 252, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }
    </style>
</head>

<body>
    <!-- Background Effects -->
    <div class="particles-container" id="particles"></div>
    <div class="brain-waves"></div>

    <div class="login-container">
        <div class="admin-badge">Admin Access</div>
        <svg class="login-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1ZM12 11.99H19C18.47 16.11 15.72 19.78 12 20.93V12H5V6.3L12 3.19V11.99Z" fill="#c084fc"/>
        </svg>
        <h2>Admin Login</h2>
        <form id="adminLoginForm">
            <input type="text" id="username" placeholder="Username" required>
            <input type="password" id="password" placeholder="Password" required>
            <button type="submit">Login</button>
            <p><a href="sleepsense_home.html">Return to Home</a></p>
        </form>
    </div>

    <script>
        /**
         * SleepSense AI Admin Login
         */
        document.addEventListener("DOMContentLoaded", function () {
            console.log('✅ Admin login script loaded');

            // Create background particles
            createParticles();

            // Initialize admin login functionality
            const adminLoginForm = document.getElementById("adminLoginForm");

            if (adminLoginForm) {
                adminLoginForm.addEventListener("submit", function (event) {
                    event.preventDefault(); // Prevent default form submission

                    const username = document.getElementById("username").value.trim();
                    const password = document.getElementById("password").value.trim();

                    // Check if fields are empty
                    if (!username || !password) {
                        alert("Please fill in all fields.");
                        return;
                    }

                    try {
                        // Send admin login request to backend
                        const response = await fetch('http://127.0.0.1:5000/api/admin_login', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ username, password })
                        });

                        const result = await response.json();

                        if (response.ok && result.status === 'success') {
                            localStorage.setItem("adminToken", "authenticated"); // Store session

                            // Show success message
                            alert("Login successful!");

                            // Redirect to admin dashboard
                            window.location.href = "sleepsense_admin_dashboard.html";
                        } else {
                            alert(result.message || "Invalid credentials. Please try again.");
                        }
                    } catch (error) {
                        console.error('Admin login error:', error);

                        // Fallback to local check if server is not available
                        if (username === "admin" && password === "admin123") {
                            localStorage.setItem("adminToken", "authenticated");
                            alert("Login successful! (Offline mode)");
                            window.location.href = "sleepsense_admin_dashboard.html";
                        } else {
                            alert("Network error. Please check if the server is running and try again.");
                        }
                    }
                });
            }
        });

        // Create background particles
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            if (!particlesContainer) return;

            // Clear existing particles
            particlesContainer.innerHTML = '';

            const colors = [
                'rgba(56, 189, 248, 0.6)',  // Blue
                'rgba(129, 140, 248, 0.6)', // Purple
                'rgba(192, 132, 252, 0.6)'  // Pink
            ];

            // Create 30 particles (reduced for better performance)
            for (let i = 0; i < 30; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';

                // Random size between 2px and 6px
                const size = Math.random() * 4 + 2;
                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;

                // Random position
                const posX = Math.random() * 100;
                const posY = Math.random() * 100;
                particle.style.left = `${posX}%`;
                particle.style.top = `${posY}%`;

                // Random color
                const colorIndex = Math.floor(Math.random() * colors.length);
                particle.style.backgroundColor = colors[colorIndex];
                particle.style.boxShadow = `0 0 ${size * 2}px ${colors[colorIndex]}`;

                // Random animation duration between 15s and 30s
                const duration = Math.random() * 15 + 15;
                particle.style.animation = `float ${duration}s ease-in-out infinite`;
                particle.style.animationDelay = `-${Math.random() * duration}s`;

                particlesContainer.appendChild(particle);
            }
        }
    </script>
</body>

</html>
