<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SleepSense AI | Sign Up</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700&family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-dark: #0f172a;
            --primary-medium: #1e293b;
            --primary-light: #334155;
            --accent-blue: #38bdf8;
            --accent-purple: #818cf8;
            --accent-pink: #c084fc;
            --text-light: #e2e8f0;
            --text-muted: #94a3b8;
            --glass-bg: rgba(15, 23, 42, 0.7);
            --glass-border: rgba(255, 255, 255, 0.1);
            --glass-shadow: rgba(0, 0, 0, 0.3);
            --transition-normal: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-medium), var(--primary-light));
            color: var(--text-light);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        /* Background Effects */
        .particles-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            border-radius: 50%;
            pointer-events: none;
        }

        .brain-waves {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.1;
            z-index: -1;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1000 1000"><path d="M0,500 Q250,400 500,500 T1000,500" stroke="white" fill="none" stroke-width="2" /></svg>') repeat-x;
            background-size: 100% 100%;
            animation: wave 15s linear infinite;
            pointer-events: none;
        }

        @keyframes wave {
            0% {
                background-position: 0 0;
            }
            100% {
                background-position: 1000px 0;
            }
        }

        .login-container {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            padding: 2.5rem;
            border-radius: 16px;
            box-shadow: 0 20px 50px var(--glass-shadow);
            border: 1px solid var(--glass-border);
            width: 90%;
            max-width: 400px;
            text-align: center;
            position: relative;
            z-index: 1;
            animation: fadeIn 1s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1.5rem;
            filter: drop-shadow(0 0 10px rgba(56, 189, 248, 0.5));
        }

        h1 {
            font-family: 'Montserrat', sans-serif;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple), var(--accent-pink));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        input {
            width: 100%;
            padding: 1rem;
            margin-bottom: 1.2rem;
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-light);
            font-family: 'Poppins', sans-serif;
            font-size: 1rem;
            transition: var(--transition-normal);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1) inset;
            text-align: center;
        }

        input:focus {
            outline: none;
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 2px rgba(56, 189, 248, 0.3), 0 4px 10px rgba(0, 0, 0, 0.1) inset;
            background: rgba(255, 255, 255, 0.08);
        }

        input::placeholder {
            color: var(--text-muted);
        }

        button {
            width: 100%;
            padding: 1rem;
            border: none;
            border-radius: 10px;
            background: linear-gradient(90deg, var(--accent-purple), var(--accent-pink));
            color: white;
            font-family: 'Poppins', sans-serif;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            box-shadow: 0 4px 15px rgba(129, 140, 248, 0.3);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, var(--accent-purple), var(--accent-pink));
            z-index: -1;
            transition: var(--transition-normal);
        }

        button:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 20px rgba(129, 140, 248, 0.5);
        }

        button:hover::before {
            transform: scale(1.1);
            opacity: 0.9;
        }

        p {
            margin-top: 1.5rem;
            color: var(--text-muted);
            font-size: 0.9rem;
        }

        a {
            color: var(--accent-blue);
            text-decoration: none;
            transition: var(--transition-normal);
            position: relative;
        }

        a::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 1px;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
            transition: var(--transition-normal);
        }

        a:hover {
            color: var(--accent-purple);
        }

        a:hover::after {
            width: 100%;
        }
    </style>
</head>

<body>
    <!-- Background Effects -->
    <div class="particles-container" id="particles"></div>
    <div class="brain-waves"></div>

    <div class="login-container">
        <svg class="login-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 4C16.0609 4 17.0783 4.42143 17.8284 5.17157C18.5786 5.92172 19 6.93913 19 8C19 9.06087 18.5786 10.0783 17.8284 10.8284C17.0783 11.5786 16.0609 12 15 12C14.47 12 13.96 11.87 13.5 11.65L11.5 13.65C11.73 14.11 11.86 14.61 11.86 15.14C11.86 16.2 11.44 17.22 10.69 17.97C9.94 18.72 8.93 19.14 7.86 19.14C6.79 19.14 5.78 18.72 5.03 17.97C4.28 17.22 3.86 16.2 3.86 15.14C3.86 14.08 4.28 13.06 5.03 12.31C5.78 11.56 6.79 11.14 7.86 11.14C8.39 11.14 8.9 11.27 9.36 11.5L11.36 9.5C11.13 9.04 11 8.53 11 8C11 6.93913 11.4214 5.92172 12.1716 5.17157C12.9217 4.42143 13.9391 4 15 4ZM15 10C15.5304 10 16.0391 9.78929 16.4142 9.41421C16.7893 9.03914 17 8.53043 17 8C17 7.46957 16.7893 6.96086 16.4142 6.58579C16.0391 6.21071 15.5304 6 15 6C14.4696 6 13.9609 6.21071 13.5858 6.58579C13.2107 6.96086 13 7.46957 13 8C13 8.53043 13.2107 9.03914 13.5858 9.41421C13.9609 9.78929 14.4696 10 15 10ZM7.86 17.14C8.39 17.14 8.9 16.93 9.28 16.55C9.66 16.17 9.86 15.66 9.86 15.14C9.86 14.61 9.66 14.1 9.28 13.72C8.9 13.34 8.39 13.14 7.86 13.14C7.33 13.14 6.83 13.34 6.45 13.72C6.07 14.1 5.86 14.61 5.86 15.14C5.86 15.66 6.07 16.17 6.45 16.55C6.83 16.93 7.33 17.14 7.86 17.14Z" fill="#818cf8"/>
        </svg>
        <h1>Sign Up</h1>
        <form id="signupForm">
            <input type="text" id="name" placeholder="Enter Name" required>
            <input type="email" id="email" placeholder="Enter Email" required>
            <input type="password" id="password" placeholder="Enter Password" required>
            <button type="submit">Sign Up</button>
            <p>Already have an account? <a href="sleepsense_login.html">Login</a></p>
            <p><a href="sleepsense_home.html">Return to Home</a></p>
        </form>
    </div>

    <script>
        /**
         * SleepSense AI Signup Page
         */
        document.addEventListener('DOMContentLoaded', function () {
            console.log('✅ Signup page script loaded');

            // Create background particles
            createParticles();

            // Initialize signup form
            const signupForm = document.getElementById('signupForm');

            if (signupForm) {
                signupForm.addEventListener('submit', function (event) {
                    event.preventDefault();  // Prevent page reload

                    const name = document.getElementById('name').value;
                    const email = document.getElementById('email').value;
                    const password = document.getElementById('password').value;

                    // Simple validation
                    if (!name || !email || !password) {
                        showToast('Please fill in all fields');
                        return;
                    }

                    // Email validation
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(email)) {
                        showToast('Please enter a valid email address');
                        return;
                    }

                    // Password validation (at least 6 characters)
                    if (password.length < 6) {
                        showToast('Password must be at least 6 characters long');
                        return;
                    }

                    // Send user data to backend API
                    const userData = {
                        name,
                        email,
                        password
                    };

                    try {
                        // Show loading message
                        showToast('Creating account...');

                        // Send signup request to backend
                        const response = await fetch('http://127.0.0.1:5000/api/signup', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(userData)
                        });

                        const result = await response.json();

                        if (response.ok) {
                            // Store current user info for the session
                            localStorage.setItem('currentUser', JSON.stringify({ name, email }));
                            localStorage.setItem('user_name', name);
                            localStorage.setItem('isLoggedIn', 'true');

                            // Show success message
                            showToast('Account created successfully!');

                            // Redirect to dashboard after a short delay
                            setTimeout(() => {
                                window.location.href = 'sleepsense_dashboard.html';
                            }, 1500);
                        } else {
                            // Show error message from server
                            showToast(result.message || 'Signup failed. Please try again.');
                        }
                    } catch (error) {
                        console.error('Signup error:', error);
                        showToast('Network error. Please check if the server is running and try again.');
                    }
                });
            } else {
                console.error('Signup form not found!');
            }
        });

        // Helper function to show toast notification
        function showToast(message) {
            // Remove existing toast if any
            const existingToast = document.querySelector('.toast-notification');
            if (existingToast) {
                existingToast.remove();
            }

            // Create toast element
            const toast = document.createElement('div');
            toast.className = 'toast-notification';
            toast.textContent = message;

            // Add toast to body
            document.body.appendChild(toast);

            // Add CSS for toast
            const style = document.createElement('style');
            style.textContent = `
                .toast-notification {
                    position: fixed;
                    bottom: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: var(--glass-bg, rgba(15, 23, 42, 0.7));
                    color: var(--text-light, #e2e8f0);
                    padding: 12px 24px;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                    z-index: 1000;
                    font-size: 0.9rem;
                    backdrop-filter: blur(10px);
                    border: 1px solid var(--glass-border, rgba(255, 255, 255, 0.1));
                    animation: toastIn 0.3s ease-out forwards, toastOut 0.3s ease-in forwards 3s;
                }

                @keyframes toastIn {
                    from { opacity: 0; transform: translate(-50%, 20px); }
                    to { opacity: 1; transform: translate(-50%, 0); }
                }

                @keyframes toastOut {
                    from { opacity: 1; transform: translate(-50%, 0); }
                    to { opacity: 0; transform: translate(-50%, 20px); }
                }
            `;
            document.head.appendChild(style);

            // Remove toast after animation
            setTimeout(() => {
                toast.remove();
                style.remove();
            }, 3500);
        }

        // Create background particles
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            if (!particlesContainer) return;

            // Clear existing particles
            particlesContainer.innerHTML = '';

            const colors = [
                'rgba(56, 189, 248, 0.6)',  // Blue
                'rgba(129, 140, 248, 0.6)', // Purple
                'rgba(192, 132, 252, 0.6)'  // Pink
            ];

            // Create 30 particles (reduced for better performance)
            for (let i = 0; i < 30; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';

                // Random size between 2px and 6px
                const size = Math.random() * 4 + 2;
                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;

                // Random position
                const posX = Math.random() * 100;
                const posY = Math.random() * 100;
                particle.style.left = `${posX}%`;
                particle.style.top = `${posY}%`;

                // Random color
                const colorIndex = Math.floor(Math.random() * colors.length);
                particle.style.backgroundColor = colors[colorIndex];
                particle.style.boxShadow = `0 0 ${size * 2}px ${colors[colorIndex]}`;

                // Random animation duration between 15s and 30s
                const duration = Math.random() * 15 + 15;
                particle.style.animation = `float ${duration}s ease-in-out infinite`;
                particle.style.animationDelay = `-${Math.random() * duration}s`;

                particlesContainer.appendChild(particle);
            }
        }
    </script>
</body>

</html>
