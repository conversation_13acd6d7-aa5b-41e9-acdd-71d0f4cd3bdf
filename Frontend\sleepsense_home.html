<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SleepSense AI | Advanced Stress Detection System</title>
    <link rel="stylesheet" href="static/css/sleepsense_main.css">
    <link rel="stylesheet" href="static/css/sleepsense_home.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>

<body>
    <!-- Background Effects -->
    <div class="particles-container" id="particles"></div>
    <div class="brain-waves"></div>
    <div id="neural-network"></div>

    <nav class="navbar">
        <div class="logo">
            <a href="#" onclick="location.reload()">
                <svg class="brain-logo" width="50" height="50" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9.5 2a2.5 2.5 0 1 1 0 5 2.5 2.5 0 0 1 0-5Z" stroke="#38bdf8" stroke-width="1.5"/>
                    <path d="M14.5 2a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5Z" stroke="#818cf8" stroke-width="1.5"/>
                    <path d="M3 10.5c0-1.1.9-2 2-2h14a2 2 0 0 1 2 2v1a2 2 0 0 1-2 2h-4l-4 2v-2H7a2 2 0 0 1-2-2v-1Z" stroke="#c084fc" stroke-width="1.5"/>
                    <path d="M12 22c-1.5-1-2-3.5-2-6h4c0 2.5-.5 5-2 6Z" stroke="#38bdf8" stroke-width="1.5"/>
                    <path d="M19 8.5V10" stroke="#818cf8" stroke-width="1.5"/>
                    <path d="M5 8.5V10" stroke="#c084fc" stroke-width="1.5"/>
                </svg>
                <span>SleepSense AI</span>
            </a>
        </div>
        <ul class="nav-list" id="nav-list">
            <li><a href="#" class="active">Home</a></li>
            <li><a href="#features">Features</a></li>
            <li><a href="#how-it-works">How It Works</a></li>
            <li><a href="#about">About Stress</a></li>
            <li><a href="#testimonials">Testimonials</a></li>
            <li><a href="#contact">Contact</a></li>
        </ul>
        <div class="auth-buttons">
            <a href="sleepsense_login.html" class="login-btn">Login</a>
            <a href="sleepsense_signup.html" class="signup-btn">Sign Up</a>
            <a href="sleepsense_admin_login.html" class="admin-btn">Admin</a>
        </div>
        <div class="burger" id="burger">
            <div class="line"></div>
            <div class="line"></div>
            <div class="line"></div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <div class="hero-text">
                <h1>Detect & Manage Stress with AI-Powered Analysis</h1>
                <p>SleepSense AI uses advanced algorithms to analyze your sleep patterns, quality, and mood to detect stress levels and provide personalized recommendations for better mental wellbeing.</p>
                <div class="hero-buttons">
                    <a href="sleepsense_signup.html" class="primary-btn">Get Started <i class="fas fa-arrow-right"></i></a>
                    <a href="#how-it-works" class="secondary-btn">Learn More <i class="fas fa-info-circle"></i></a>
                </div>
                <div class="hero-stats">
                    <div class="stat">
                        <span class="stat-number">94%</span>
                        <span class="stat-text">Accuracy Rate</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">10k+</span>
                        <span class="stat-text">Active Users</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">4.8</span>
                        <span class="stat-text">User Rating</span>
                    </div>
                </div>
            </div>
            <div class="hero-image">
                <img src="https://img.freepik.com/free-vector/mental-health-awareness-concept_23-**********.jpg" alt="AI Stress Detection">
                <div class="hero-blob"></div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <div class="section-header">
            <h2>Powerful Features</h2>
            <p>Discover how SleepSense AI helps you understand and manage your stress levels</p>
        </div>
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <h3>AI-Powered Analysis</h3>
                <p>Our advanced algorithms analyze multiple factors to accurately detect stress patterns and provide personalized insights.</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3>3D Visualization</h3>
                <p>Interactive 3D graphs help you visualize the relationship between sleep, mood, and stress levels.</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-bed"></i>
                </div>
                <h3>Sleep Pattern Analysis</h3>
                <p>Track and analyze your sleep duration and quality to identify how they impact your stress levels.</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-smile"></i>
                </div>
                <h3>Mood Tracking</h3>
                <p>Record your daily mood to understand how emotional states correlate with stress and sleep patterns.</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-lightbulb"></i>
                </div>
                <h3>Personalized Tips</h3>
                <p>Receive customized recommendations to improve sleep quality and reduce stress based on your data.</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-file-pdf"></i>
                </div>
                <h3>Exportable Reports</h3>
                <p>Generate and download detailed PDF reports of your stress analysis to share with healthcare providers.</p>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="how-it-works" id="how-it-works">
        <div class="section-header">
            <h2>How It Works</h2>
            <p>Our stress detection system works in four simple steps</p>
        </div>
        <div class="steps">
            <div class="step">
                <div class="step-number">1</div>
                <div class="step-content">
                    <h3>Input Your Data</h3>
                    <p>Enter your sleep hours (0-24), rate your sleep quality (0-10), and select your current mood from the dropdown menu.</p>
                    <img src="https://img.freepik.com/free-vector/online-questionnaire-concept-illustration_114360-5212.jpg" alt="Input Data">
                </div>
            </div>
            <div class="step">
                <div class="step-number">2</div>
                <div class="step-content">
                    <h3>AI Analysis</h3>
                    <p>Our advanced algorithm processes your data, considering the relationships between sleep patterns, quality, and mood states.</p>
                    <img src="https://img.freepik.com/free-vector/data-extraction-concept-illustration_114360-4876.jpg" alt="AI Analysis">
                </div>
            </div>
            <div class="step">
                <div class="step-number">3</div>
                <div class="step-content">
                    <h3>Visualization</h3>
                    <p>View your data in an interactive 3D graph that clearly shows the relationship between all factors and your stress level.</p>
                    <img src="https://img.freepik.com/free-vector/data-inform-concept-illustration_114360-656.jpg" alt="Visualization">
                </div>
            </div>
            <div class="step">
                <div class="step-number">4</div>
                <div class="step-content">
                    <h3>Personalized Insights</h3>
                    <p>Receive a detailed analysis of your stress level with personalized recommendations to improve your wellbeing.</p>
                    <img src="https://img.freepik.com/free-vector/health-tips-concept-illustration_114360-7441.jpg" alt="Personalized Insights">
                </div>
            </div>
        </div>
        <div class="cta-container">
            <a href="#features" class="secondary-btn">Explore Features <i class="fas fa-search"></i></a>
        </div>
    </section>

    <!-- About Stress Section -->
    <section class="about-stress" id="about">
        <div class="section-header">
            <h2>Understanding Stress</h2>
            <p>Learn about different types of stress and how they affect your body and mind</p>
        </div>
        <div class="stress-tabs">
            <div class="tabs-nav">
                <button class="tab-btn active" data-tab="what-is-stress">What is Stress?</button>
                <button class="tab-btn" data-tab="types-of-stress">Types of Stress</button>
                <button class="tab-btn" data-tab="physical-effects">Physical Effects</button>
                <button class="tab-btn" data-tab="management">Stress Management</button>
            </div>
            <div class="tabs-content">
                <div class="tab-content active" id="what-is-stress">
                    <div class="tab-text">
                        <h3>What is Stress?</h3>
                        <p>Stress is your body's natural response to pressure from life situations. Everyone experiences stress to some degree. The way you respond to stress, however, makes a big difference to your overall well-being.</p>
                        <p>When you experience changes or challenges (stressors), your body produces physical and mental responses. These responses help your body adjust to new situations. Stress can be positive, keeping you alert, motivated and ready to avoid danger. For example, if you have an important test coming up, your stress response might help you stay awake longer and study harder.</p>
                        <p>But stress becomes a problem when stressors continue without relief or periods of relaxation. This can lead to a condition called chronic stress, which can negatively affect your health.</p>
                    </div>
                    <div class="tab-image">
                        <img src="https://img.freepik.com/free-vector/anxiety-concept-illustration_114360-8074.jpg" alt="What is Stress">
                    </div>
                </div>
                <div class="tab-content" id="types-of-stress">
                    <div class="tab-text">
                        <h3>Types of Stress</h3>
                        <p>There are three main types of stress, each with different characteristics, symptoms, duration, and treatment approaches:</p>
                        <h4>Acute Stress</h4>
                        <p>Acute stress is short-term stress that goes away quickly. It helps you manage dangerous situations. It also occurs when you do something new or exciting. All people have acute stress at one time or another.</p>
                        <h4>Episodic Acute Stress</h4>
                        <p>Episodic acute stress happens when you frequently experience acute stress. This type of stress applies to people who are always in a rush, take on too many responsibilities, or worry excessively.</p>
                        <h4>Chronic Stress</h4>
                        <p>Chronic stress is stress that lasts for a longer period of time. You may experience chronic stress if you have ongoing financial problems, an unhappy marriage, or trouble at work. Any type of stress that continues for weeks or months is chronic stress.</p>
                    </div>
                    <div class="tab-image">
                        <img src="https://img.freepik.com/free-vector/stress-management-concept-illustration_114360-5870.jpg" alt="Types of Stress">
                    </div>
                </div>
                <div class="tab-content" id="physical-effects">
                    <div class="tab-text">
                        <h3>Physical Effects of Stress</h3>
                        <p>Your body's autonomic nervous system controls your heart rate, breathing, vision changes, and more. Its built-in stress response, the "fight-or-flight response," helps your body face stressful situations.</p>
                        <p>When you have chronic stress, continued activation of the stress response causes wear and tear on your body. Physical symptoms may include:</p>
                        <ul class="effects-list">
                            <li><i class="fas fa-heartbeat"></i> Chest pain or racing heart</li>
                            <li><i class="fas fa-head-side-virus"></i> Headaches and dizziness</li>
                            <li><i class="fas fa-bed"></i> Insomnia or sleep problems</li>
                            <li><i class="fas fa-weight-scale"></i> Weight gain or loss</li>
                            <li><i class="fas fa-stomach"></i> Digestive problems</li>
                            <li><i class="fas fa-virus"></i> Weakened immune system</li>
                            <li><i class="fas fa-dumbbell"></i> Muscle tension and pain</li>
                            <li><i class="fas fa-gauge-high"></i> High blood pressure</li>
                        </ul>
                    </div>
                    <div class="tab-image">
                        <img src="https://img.freepik.com/free-vector/burnout-concept-illustration_114360-7316.jpg" alt="Physical Effects of Stress">
                    </div>
                </div>
                <div class="tab-content" id="management">
                    <div class="tab-text">
                        <h3>Stress Management Techniques</h3>
                        <p>Managing stress is crucial for maintaining both physical and mental health. Here are some effective strategies:</p>
                        <div class="management-techniques">
                            <div class="technique">
                                <i class="fas fa-heart-pulse"></i>
                                <h4>Physical Activity</h4>
                                <p>Regular exercise can help reduce stress hormones and improve mood.</p>
                            </div>
                            <div class="technique">
                                <i class="fas fa-spa"></i>
                                <h4>Mindfulness & Meditation</h4>
                                <p>Practices that help focus your attention and reduce negative thinking.</p>
                            </div>
                            <div class="technique">
                                <i class="fas fa-moon"></i>
                                <h4>Healthy Sleep Habits</h4>
                                <p>Consistent sleep schedule and quality rest help manage stress levels.</p>
                            </div>
                            <div class="technique">
                                <i class="fas fa-utensils"></i>
                                <h4>Balanced Nutrition</h4>
                                <p>A healthy diet supports your body's ability to cope with stress.</p>
                            </div>
                        </div>
                    </div>
                    <div class="tab-image">
                        <img src="https://img.freepik.com/free-vector/meditation-concept-illustration_114360-2045.jpg" alt="Stress Management">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials" id="testimonials">
        <div class="section-header">
            <h2>What Our Users Say</h2>
            <p>Discover how SleepSense AI has helped people manage their stress and improve wellbeing</p>
        </div>
        <div class="testimonials-slider">
            <div class="testimonial">
                <div class="testimonial-content">
                    <div class="quote-icon"><i class="fas fa-quote-left"></i></div>
                    <p>SleepSense AI has completely changed how I understand my stress levels. The correlation between my poor sleep and high stress was eye-opening. Following the personalized recommendations has improved my sleep quality dramatically.</p>
                    <div class="testimonial-author">
                        <img src="https://randomuser.me/api/portraits/women/32.jpg" alt="Sarah Johnson">
                        <div class="author-info">
                            <h4>Sarah Johnson</h4>
                            <p>Marketing Executive</p>
                            <div class="rating">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="testimonial">
                <div class="testimonial-content">
                    <div class="quote-icon"><i class="fas fa-quote-left"></i></div>
                    <p>As a medical professional with irregular shifts, tracking my sleep patterns and stress levels has been invaluable. The 3D visualization makes it easy to see how my changing schedule affects my wellbeing. Highly recommended!</p>
                    <div class="testimonial-author">
                        <img src="https://randomuser.me/api/portraits/men/45.jpg" alt="Dr. Michael Chen">
                        <div class="author-info">
                            <h4>Dr. Michael Chen</h4>
                            <p>Emergency Physician</p>
                            <div class="rating">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star-half-alt"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="testimonial">
                <div class="testimonial-content">
                    <div class="quote-icon"><i class="fas fa-quote-left"></i></div>
                    <p>I've tried many stress management apps, but SleepSense AI is the first one that actually provides actionable insights. The mood tracking feature helped me identify emotional patterns I wasn't aware of. It's been transformative.</p>
                    <div class="testimonial-author">
                        <img src="https://randomuser.me/api/portraits/women/65.jpg" alt="Emily Rodriguez">
                        <div class="author-info">
                            <h4>Emily Rodriguez</h4>
                            <p>Software Developer</p>
                            <div class="rating">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="cta-section">
        <div class="cta-content">
            <h2>Ready to Take Control of Your Stress?</h2>
            <p>Join thousands of users who have improved their wellbeing with SleepSense AI's advanced stress detection system.</p>
            <div class="cta-buttons">
                <a href="sleepsense_signup.html" class="primary-btn">Get Started Now <i class="fas fa-rocket"></i></a>
            </div>
        </div>
        <div class="cta-image">
            <img src="https://img.freepik.com/free-vector/mental-health-awareness-concept_23-**********.jpg" alt="Take Control of Stress">
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact" id="contact">
        <div class="section-header">
            <h2>Get in Touch</h2>
            <p>Have questions or feedback? We'd love to hear from you!</p>
        </div>
        <div class="contact-container">
            <div class="contact-info">
                <div class="info-item">
                    <i class="fas fa-envelope"></i>
                    <h3>Email Us</h3>
                    <p><EMAIL></p>
                </div>
                <div class="info-item">
                    <i class="fas fa-phone-alt"></i>
                    <h3>Call Us</h3>
                    <p>+****************</p>
                </div>
                <div class="info-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <h3>Visit Us</h3>
                    <p>123 Innovation Drive<br>Tech Valley, CA 94043</p>
                </div>
                <div class="social-links">
                    <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
                    <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                    <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                    <a href="#" class="social-link"><i class="fab fa-linkedin-in"></i></a>
                </div>
            </div>
            <div class="contact-form">
                <form id="contactForm">
                    <div class="form-group">
                        <label for="name">Your Name</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="email">Your Email</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="subject">Subject</label>
                        <input type="text" id="subject" name="subject" required>
                    </div>
                    <div class="form-group">
                        <label for="message">Your Message</label>
                        <textarea id="message" name="message" rows="5" required></textarea>
                    </div>
                    <div id="formStatus" class="form-status"></div>
                    <button type="submit" class="submit-btn" id="submitBtn">Send Message <i class="fas fa-paper-plane"></i></button>
                </form>
            </div>

            <!-- Toast notification for form submission -->
            <div id="contactToast" class="contact-toast">
                <div class="toast-icon"><i class="fas fa-check-circle"></i></div>
                <div class="toast-message">Thank you for your message! We will get back to you soon.</div>
            </div>

            <style>
                .form-status {
                    margin-bottom: 15px;
                    padding: 10px;
                    border-radius: 5px;
                    display: none;
                }

                .form-status.success {
                    display: block;
                    background-color: rgba(16, 185, 129, 0.1);
                    border: 1px solid rgba(16, 185, 129, 0.3);
                    color: #10b981;
                }

                .form-status.error {
                    display: block;
                    background-color: rgba(239, 68, 68, 0.1);
                    border: 1px solid rgba(239, 68, 68, 0.3);
                    color: #ef4444;
                }

                .contact-toast {
                    position: fixed;
                    bottom: -100px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: var(--glass-bg, rgba(15, 23, 42, 0.8));
                    backdrop-filter: blur(10px);
                    border: 1px solid var(--glass-border, rgba(255, 255, 255, 0.1));
                    color: white;
                    padding: 15px 25px;
                    border-radius: 8px;
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
                    z-index: 1000;
                    transition: bottom 0.5s ease;
                }

                .contact-toast.show {
                    bottom: 30px;
                }

                .toast-icon {
                    font-size: 1.5rem;
                    color: #10b981;
                }

                .toast-message {
                    font-size: 0.95rem;
                }

                .submit-btn {
                    position: relative;
                    overflow: hidden;
                }

                .submit-btn.loading {
                    pointer-events: none;
                    opacity: 0.8;
                }

                .submit-btn.loading::after {
                    content: "";
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 300%;
                    height: 100%;
                    background: linear-gradient(
                        90deg,
                        transparent,
                        rgba(255, 255, 255, 0.2),
                        transparent
                    );
                    animation: loading 1.5s infinite;
                }

                @keyframes loading {
                    0% { left: -100%; }
                    100% { left: 100%; }
                }
            </style>
        </div>
    </section>



    <!-- Footer -->
    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <svg class="brain-logo" width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9.5 2a2.5 2.5 0 1 1 0 5 2.5 2.5 0 0 1 0-5Z" stroke="#38bdf8" stroke-width="1.5"/>
                    <path d="M14.5 2a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5Z" stroke="#818cf8" stroke-width="1.5"/>
                    <path d="M3 10.5c0-1.1.9-2 2-2h14a2 2 0 0 1 2 2v1a2 2 0 0 1-2 2h-4l-4 2v-2H7a2 2 0 0 1-2-2v-1Z" stroke="#c084fc" stroke-width="1.5"/>
                    <path d="M12 22c-1.5-1-2-3.5-2-6h4c0 2.5-.5 5-2 6Z" stroke="#38bdf8" stroke-width="1.5"/>
                </svg>
                <span>SleepSense AI</span>
            </div>
            <div class="footer-links">
                <div class="footer-column">
                    <h3>Product</h3>
                    <ul>
                        <li><a href="#features">Features</a></li>
                        <li><a href="#how-it-works">How It Works</a></li>
                        <li><a href="#">Pricing</a></li>
                        <li><a href="#">FAQ</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Company</h3>
                    <ul>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Careers</a></li>
                        <li><a href="#">Blog</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Legal</h3>
                    <ul>
                        <li><a href="#">Terms of Service</a></li>
                        <li><a href="#">Privacy Policy</a></li>
                        <li><a href="#">Cookie Policy</a></li>
                        <li><a href="#">GDPR</a></li>
                    </ul>
                </div>
                <div class="footer-column">
                    <h3>Connect</h3>
                    <div class="social-links">
                        <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                    <div class="app-links">
                        <a href="#" class="app-link"><img src="https://img.icons8.com/ios-filled/50/ffffff/app-store.png" alt="App Store">App Store</a>
                        <a href="#" class="app-link"><img src="https://img.icons8.com/ios-filled/50/ffffff/google-play.png" alt="Google Play">Google Play</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2023 SleepSense AI. All rights reserved.</p>
            <p>Made with <i class="fas fa-heart"></i> for better mental health</p>
        </div>
    </footer>

    <script>
        /**
         * SleepSense AI Home Page
         */
        document.addEventListener('DOMContentLoaded', () => {
            console.log('✅ Home page script loaded');

            // Create background particles
            createParticles();

            // Create neural network if element exists
            const neuralNetwork = document.getElementById('neural-network');
            if (neuralNetwork) {
                createNeuralNetwork();
            }

            // Initialize tab functionality
            initTabs();

            // Mobile menu toggle
            const burger = document.getElementById('burger');
            const navList = document.getElementById('nav-list');

            if (burger && navList) {
                burger.addEventListener('click', () => {
                    burger.classList.toggle('active');
                    navList.classList.toggle('active');
                });

                // Close menu when clicking outside
                document.addEventListener('click', (e) => {
                    if (!burger.contains(e.target) && !navList.contains(e.target) && navList.classList.contains('active')) {
                        burger.classList.remove('active');
                        navList.classList.remove('active');
                    }
                });
            }

            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    if (this.getAttribute('href') !== '#') {
                        e.preventDefault();

                        const targetId = this.getAttribute('href');
                        const targetElement = document.querySelector(targetId);

                        if (targetElement) {
                            // Close mobile menu if open
                            if (burger && navList && navList.classList.contains('active')) {
                                burger.classList.remove('active');
                                navList.classList.remove('active');
                            }

                            window.scrollTo({
                                top: targetElement.offsetTop - 80, // Adjust for navbar height
                                behavior: 'smooth'
                            });

                            // Update active nav link
                            document.querySelectorAll('.nav-list a').forEach(link => {
                                link.classList.remove('active');
                            });
                            this.classList.add('active');
                        }
                    }
                });
            });

            // Update active nav link on scroll
            window.addEventListener('scroll', () => {
                const scrollPosition = window.scrollY;

                document.querySelectorAll('section[id]').forEach(section => {
                    const sectionTop = section.offsetTop - 100;
                    const sectionHeight = section.offsetHeight;
                    const sectionId = section.getAttribute('id');

                    if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                        document.querySelectorAll('.nav-list a').forEach(link => {
                            link.classList.remove('active');
                            if (link.getAttribute('href') === `#${sectionId}`) {
                                link.classList.add('active');
                            }
                        });
                    }
                });
            });

            // Add animation to elements when they come into view
            const animateOnScroll = () => {
                const elements = document.querySelectorAll('.feature-card, .step, .testimonial, .info-item');

                elements.forEach(element => {
                    const elementPosition = element.getBoundingClientRect().top;
                    const screenPosition = window.innerHeight;

                    if (elementPosition < screenPosition - 100) {
                        element.style.opacity = '1';
                        element.style.transform = 'translateY(0)';
                    }
                });
            };

            // Set initial styles for animation
            document.querySelectorAll('.feature-card, .step, .testimonial, .info-item').forEach(element => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';
                element.style.transition = 'opacity 0.5s ease-out, transform 0.5s ease-out';
            });

            // Run animation on scroll
            window.addEventListener('scroll', animateOnScroll);
            // Run once on page load
            animateOnScroll();

            // Contact form submission
            document.getElementById('contactForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const form = e.target;
                const submitBtn = document.getElementById('submitBtn');
                const formStatus = document.getElementById('formStatus');
                const contactToast = document.getElementById('contactToast');

                // Get form values
                const name = form.querySelector('#name').value.trim();
                const email = form.querySelector('#email').value.trim();
                const subject = form.querySelector('#subject').value.trim();
                const message = form.querySelector('#message').value.trim();

                // Basic validation
                if (!name || !email || !subject || !message) {
                    formStatus.textContent = "Please fill in all fields";
                    formStatus.className = "form-status error";
                    return;
                }

                // Email validation
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    formStatus.textContent = "Please enter a valid email address";
                    formStatus.className = "form-status error";
                    return;
                }

                // Show loading state
                submitBtn.classList.add('loading');
                submitBtn.innerHTML = 'Sending... <i class="fas fa-spinner fa-spin"></i>';
                formStatus.style.display = 'none';

                // Create contact object
                const contactData = {
                    name,
                    email,
                    concern: message
                };

                // Send contact data to backend API
                try {
                    // Send contact request to backend
                    const response = await fetch('http://127.0.0.1:5000/api/contact', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(contactData)
                    });

                    const result = await response.json();

                    if (response.ok) {
                        // Reset form
                        form.reset();

                        // Show success message
                        formStatus.textContent = "Message sent successfully!";
                        formStatus.className = "form-status success";

                        // Show toast notification
                        contactToast.classList.add('show');
                        setTimeout(() => {
                            contactToast.classList.remove('show');
                        }, 5000);

                        console.log('Contact form submission successful:', result);
                    } else {
                        // Show error message from server
                        formStatus.textContent = result.message || "Failed to send message. Please try again.";
                        formStatus.className = "form-status error";
                    }
                } catch (error) {
                    console.error('Error submitting contact form:', error);
                    formStatus.textContent = "Network error. Please check if the server is running and try again.";
                    formStatus.className = "form-status error";
                } finally {
                    // Reset button state
                    submitBtn.classList.remove('loading');
                    submitBtn.innerHTML = 'Send Message <i class="fas fa-paper-plane"></i>';
                }
            });
        });

        // Tab functionality
        function initTabs() {
            const tabButtons = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');

            if (tabButtons.length === 0 || tabContents.length === 0) return;

            // Show first tab by default
            tabContents[0].classList.add('active');
            tabButtons[0].classList.add('active');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Remove active class from all buttons and contents
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));

                    // Add active class to clicked button
                    button.classList.add('active');

                    // Show corresponding content
                    const tabId = button.getAttribute('data-tab');
                    const tabContent = document.getElementById(tabId);
                    if (tabContent) {
                        tabContent.classList.add('active');
                    }
                });
            });
        }

        // Create background particles
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            if (!particlesContainer) return;

            // Clear existing particles
            particlesContainer.innerHTML = '';

            const colors = [
                'rgba(56, 189, 248, 0.6)',  // Blue
                'rgba(129, 140, 248, 0.6)', // Purple
                'rgba(192, 132, 252, 0.6)'  // Pink
            ];

            // Create 30 particles (reduced for better performance)
            for (let i = 0; i < 30; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';

                // Random size between 2px and 6px
                const size = Math.random() * 4 + 2;
                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;

                // Random position
                const posX = Math.random() * 100;
                const posY = Math.random() * 100;
                particle.style.left = `${posX}%`;
                particle.style.top = `${posY}%`;

                // Random color
                const colorIndex = Math.floor(Math.random() * colors.length);
                particle.style.backgroundColor = colors[colorIndex];
                particle.style.boxShadow = `0 0 ${size * 2}px ${colors[colorIndex]}`;

                // Random animation duration between 15s and 30s
                const duration = Math.random() * 15 + 15;
                particle.style.animation = `float ${duration}s ease-in-out infinite`;
                particle.style.animationDelay = `-${Math.random() * duration}s`;

                particlesContainer.appendChild(particle);
            }
        }

        // Create neural network visualization
        function createNeuralNetwork() {
            const neuralNetwork = document.getElementById('neural-network');
            if (!neuralNetwork) return;

            // Clear existing network
            neuralNetwork.innerHTML = '';

            const neurons = [];
            const numNeurons = 15;

            // Create neurons
            for (let i = 0; i < numNeurons; i++) {
                const neuron = document.createElement('div');
                neuron.classList.add('neuron');

                // Position randomly but more towards the center
                const x = 30 + Math.random() * 40; // 30% to 70% of screen width
                const y = 30 + Math.random() * 40; // 30% to 70% of screen height

                neuron.style.left = x + 'vw';
                neuron.style.top = y + 'vh';

                neurons.push({ element: neuron, x, y });
                neuralNetwork.appendChild(neuron);
            }

            // Create synapses (connections between neurons)
            for (let i = 0; i < neurons.length; i++) {
                const neuron = neurons[i];

                // Connect to 2-4 other random neurons
                const connections = Math.floor(Math.random() * 3) + 2;

                for (let j = 0; j < connections; j++) {
                    // Select a random target neuron that's not the current one
                    let targetIndex;
                    do {
                        targetIndex = Math.floor(Math.random() * neurons.length);
                    } while (targetIndex === i);

                    const target = neurons[targetIndex];

                    // Calculate distance and angle
                    const dx = target.x - neuron.x;
                    const dy = target.y - neuron.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);
                    const angle = Math.atan2(dy, dx) * 180 / Math.PI;

                    // Create synapse
                    const synapse = document.createElement('div');
                    synapse.classList.add('synapse');
                    synapse.style.left = neuron.x + 'vw';
                    synapse.style.top = neuron.y + 'vh';
                    synapse.style.width = distance + 'vw';
                    synapse.style.transform = `rotate(${angle}deg)`;

                    neuralNetwork.appendChild(synapse);
                }
            }
        }
    </script>
</body>
</html>
