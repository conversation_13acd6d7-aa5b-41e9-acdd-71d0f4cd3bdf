/* Home Page Specific Styles */

/* Navbar Enhancements */
.navbar {
  padding: 1rem 5%;
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
}

.nav-list a.active {
  color: var(--accent-blue);
}

.nav-list a.active::after {
  width: 100%;
}

.auth-buttons {
  display: flex;
  gap: 1rem;
}

.login-btn, .signup-btn, .admin-btn {
  padding: 0.5rem 1.5rem;
  border-radius: 50px;
  font-weight: 600;
  transition: var(--transition-normal);
  text-decoration: none;
  margin-left: 0.5rem;
}

.login-btn {
  color: var(--accent-blue);
  border: 2px solid var(--accent-blue);
  background: transparent;
}

.login-btn:hover {
  background: rgba(56, 189, 248, 0.1);
  transform: translateY(-3px);
}

.signup-btn {
  background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
  color: white;
  box-shadow: 0 4px 15px rgba(56, 189, 248, 0.3);
}

.signup-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 7px 20px rgba(56, 189, 248, 0.5);
}

.admin-btn {
  background: linear-gradient(90deg, var(--accent-purple), var(--accent-pink));
  color: white;
  box-shadow: 0 4px 15px rgba(192, 132, 252, 0.3);
}

.admin-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 7px 20px rgba(192, 132, 252, 0.5);
}

/* Hero Section */
.hero {
  padding: 5rem 5%;
  min-height: calc(100vh - 80px);
  display: flex;
  align-items: center;
}

.hero-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 4rem;
  max-width: 1400px;
  margin: 0 auto;
}

.hero-text {
  flex: 1;
  animation: fadeIn 1s ease-out;
}

.hero-text h1 {
  font-family: 'Montserrat', sans-serif;
  font-size: 3rem;
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple), var(--accent-pink));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.hero-text p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-muted);
  margin-bottom: 2rem;
  max-width: 600px;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 2.5rem;
}

.primary-btn, .secondary-btn {
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  transition: var(--transition-normal);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.primary-btn {
  background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
  color: white;
  box-shadow: 0 4px 15px rgba(56, 189, 248, 0.3);
}

.primary-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 7px 20px rgba(56, 189, 248, 0.5);
}

.secondary-btn {
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-light);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.secondary-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-3px);
}

.hero-stats {
  display: flex;
  gap: 2rem;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-family: 'Montserrat', sans-serif;
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.stat-text {
  font-size: 0.9rem;
  color: var(--text-muted);
}

.hero-image {
  flex: 1;
  position: relative;
  animation: fadeIn 1s ease-out 0.3s both;
}

.hero-image img {
  max-width: 100%;
  border-radius: 16px;
  box-shadow: 0 20px 50px var(--glass-shadow);
  position: relative;
  z-index: 2;
}

.hero-blob {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 10%;
  left: 10%;
  background: linear-gradient(45deg, var(--accent-blue), var(--accent-purple));
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  filter: blur(30px);
  opacity: 0.3;
  z-index: 1;
  animation: blob-animation 8s infinite alternate;
}

@keyframes blob-animation {
  0% {
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  }
  50% {
    border-radius: 70% 30% 30% 70% / 70% 70% 30% 30%;
  }
  100% {
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  }
}

/* Section Styles */
.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-header h2 {
  font-family: 'Montserrat', sans-serif;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.section-header p {
  font-size: 1.1rem;
  color: var(--text-muted);
  max-width: 600px;
  margin: 0 auto;
}

/* Features Section */
.features {
  padding: 5rem 5%;
  background: linear-gradient(180deg, var(--primary-dark), var(--primary-medium));
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.feature-card {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 10px 30px var(--glass-shadow);
  border: 1px solid var(--glass-border);
  transition: var(--transition-normal);
  height: 100%;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px var(--glass-shadow);
  border-color: var(--accent-blue);
}

.feature-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--accent-blue), var(--accent-purple));
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  color: white;
  box-shadow: 0 5px 15px rgba(56, 189, 248, 0.3);
}

.feature-card h3 {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-light);
}

.feature-card p {
  color: var(--text-muted);
  line-height: 1.6;
}

/* How It Works Section */
.how-it-works {
  padding: 5rem 5%;
  background: linear-gradient(180deg, var(--primary-medium), var(--primary-dark));
}

.steps {
  max-width: 1000px;
  margin: 0 auto;
}

.step {
  display: flex;
  gap: 2rem;
  margin-bottom: 4rem;
  position: relative;
}

.step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 70px;
  left: 30px;
  width: 2px;
  height: calc(100% + 4rem);
  background: linear-gradient(to bottom, var(--accent-blue), var(--accent-purple));
  z-index: 1;
}

.step-number {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--accent-blue), var(--accent-purple));
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Montserrat', sans-serif;
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  box-shadow: 0 5px 15px rgba(56, 189, 248, 0.3);
  flex-shrink: 0;
  z-index: 2;
}

.step-content {
  flex: 1;
}

.step-content h3 {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-light);
}

.step-content p {
  color: var(--text-muted);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.step-content img {
  max-width: 100%;
  border-radius: 12px;
  box-shadow: 0 10px 30px var(--glass-shadow);
}

.cta-container {
  text-align: center;
  margin-top: 2rem;
}

/* About Stress Section */
.about-stress {
  padding: 5rem 5%;
  background: linear-gradient(180deg, var(--primary-dark), var(--primary-medium));
}

.stress-tabs {
  max-width: 1200px;
  margin: 0 auto;
}

.tabs-nav {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.tab-btn {
  padding: 0.8rem 1.5rem;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 50px;
  color: var(--text-muted);
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-normal);
}

.tab-btn.active, .tab-btn:hover {
  background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
  color: white;
  border-color: transparent;
  box-shadow: 0 5px 15px rgba(56, 189, 248, 0.3);
}

.tab-content {
  display: none;
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 10px 30px var(--glass-shadow);
  border: 1px solid var(--glass-border);
  animation: fadeIn 0.5s ease-out;
}

.tab-content.active {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.tab-text {
  flex: 1;
  min-width: 300px;
}

.tab-text h3 {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-light);
}

.tab-text h4 {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 1.5rem 0 0.5rem;
  color: var(--accent-blue);
}

.tab-text p {
  color: var(--text-muted);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.tab-image {
  flex: 1;
  min-width: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-image img {
  max-width: 100%;
  border-radius: 12px;
  box-shadow: 0 10px 30px var(--glass-shadow);
}

.effects-list {
  list-style: none;
  margin-top: 1rem;
}

.effects-list li {
  margin-bottom: 0.8rem;
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.effects-list li i {
  color: var(--accent-blue);
  font-size: 1.2rem;
  width: 24px;
  text-align: center;
}

.management-techniques {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.technique {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  transition: var(--transition-normal);
}

.technique:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
}

.technique i {
  font-size: 2rem;
  color: var(--accent-purple);
  margin-bottom: 1rem;
}

.technique h4 {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.8rem;
  color: var(--text-light);
}

.technique p {
  color: var(--text-muted);
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Testimonials Section */
.testimonials {
  padding: 5rem 5%;
  background: linear-gradient(180deg, var(--primary-medium), var(--primary-dark));
}

.testimonials-slider {
  display: flex;
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  overflow-x: auto;
  padding: 1rem 0.5rem;
  scrollbar-width: thin;
  scrollbar-color: var(--accent-blue) var(--primary-dark);
}

.testimonials-slider::-webkit-scrollbar {
  height: 8px;
}

.testimonials-slider::-webkit-scrollbar-track {
  background: var(--primary-dark);
  border-radius: 4px;
}

.testimonials-slider::-webkit-scrollbar-thumb {
  background: var(--accent-blue);
  border-radius: 4px;
}

.testimonial {
  flex: 0 0 350px;
  max-width: 350px;
}

.testimonial-content {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 10px 30px var(--glass-shadow);
  border: 1px solid var(--glass-border);
  height: 100%;
  transition: var(--transition-normal);
}

.testimonial-content:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px var(--glass-shadow);
  border-color: var(--accent-purple);
}

.quote-icon {
  color: var(--accent-purple);
  font-size: 2rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.testimonial-content > p {
  color: var(--text-muted);
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-style: italic;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.testimonial-author img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--accent-blue);
}

.author-info h4 {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-light);
}

.author-info p {
  color: var(--text-muted);
  font-size: 0.9rem;
}

.rating {
  margin-top: 0.5rem;
  color: #f59e0b;
}

/* Call to Action Section */
.cta-section {
  padding: 5rem 5%;
  background: linear-gradient(180deg, var(--primary-dark), var(--primary-medium));
  display: flex;
  align-items: center;
  gap: 4rem;
  max-width: 1400px;
  margin: 0 auto;
}

.cta-content {
  flex: 1;
}

.cta-content h2 {
  font-family: 'Montserrat', sans-serif;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple), var(--accent-pink));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.cta-content p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-muted);
  margin-bottom: 2rem;
  max-width: 600px;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
}

.cta-image {
  flex: 1;
  display: flex;
  justify-content: center;
}

.cta-image img {
  max-width: 100%;
  border-radius: 16px;
  box-shadow: 0 20px 50px var(--glass-shadow);
}

/* Contact Section */
.contact {
  padding: 5rem 5%;
  background: linear-gradient(180deg, var(--primary-medium), var(--primary-dark));
}

.contact-container {
  display: flex;
  gap: 3rem;
  max-width: 1200px;
  margin: 0 auto;
  flex-wrap: wrap;
}

.contact-info {
  flex: 1;
  min-width: 300px;
}

.info-item {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 10px 30px var(--glass-shadow);
  border: 1px solid var(--glass-border);
  margin-bottom: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: var(--transition-normal);
}

.info-item:hover {
  transform: translateY(-5px);
  border-color: var(--accent-blue);
}

.info-item i {
  font-size: 2rem;
  color: var(--accent-blue);
  margin-bottom: 1rem;
}

.info-item h3 {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-light);
}

.info-item p {
  color: var(--text-muted);
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
}

.social-link {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--glass-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  transition: var(--transition-normal);
  border: 1px solid var(--glass-border);
}

.social-link:hover {
  background: linear-gradient(45deg, var(--accent-blue), var(--accent-purple));
  color: white;
  transform: translateY(-3px);
}

.contact-form {
  flex: 1;
  min-width: 300px;
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 10px 30px var(--glass-shadow);
  border: 1px solid var(--glass-border);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-muted);
  font-size: 0.9rem;
}

.form-group input, .form-group textarea {
  width: 100%;
  padding: 0.8rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--glass-border);
  border-radius: 8px;
  color: var(--text-light);
  font-family: 'Poppins', sans-serif;
  transition: var(--transition-normal);
}

.form-group input:focus, .form-group textarea:focus {
  outline: none;
  border-color: var(--accent-blue);
  box-shadow: 0 0 0 2px rgba(56, 189, 248, 0.3);
}

.submit-btn {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
  color: white;
  border: none;
  border-radius: 8px;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.submit-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 7px 20px rgba(56, 189, 248, 0.3);
}

/* Admin Access Section */
.admin-access {
  padding: 3rem 5%;
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-medium));
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.admin-access-content {
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2.5rem;
  box-shadow: 0 10px 30px var(--glass-shadow);
  border: 1px solid var(--glass-border);
}

.admin-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--accent-purple), var(--accent-pink));
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 2.5rem;
  color: white;
  box-shadow: 0 10px 25px rgba(192, 132, 252, 0.4);
}

.admin-access-content h3 {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-light);
}

.admin-access-content p {
  color: var(--text-muted);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.admin-access-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  background: linear-gradient(90deg, var(--accent-purple), var(--accent-pink));
  color: white;
  border-radius: 50px;
  font-weight: 600;
  text-decoration: none;
  transition: var(--transition-normal);
  box-shadow: 0 8px 20px rgba(192, 132, 252, 0.3);
}

.admin-access-btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(192, 132, 252, 0.5);
}

/* Footer Enhancements */
footer {
  padding: 4rem 5% 2rem;
  background: var(--primary-dark);
}

.footer-content {
  display: flex;
  flex-direction: column;
  gap: 3rem;
  max-width: 1400px;
  margin: 0 auto;
}

.footer-links {
  display: flex;
  flex-wrap: wrap;
  gap: 3rem;
}

.footer-column {
  flex: 1;
  min-width: 200px;
}

.footer-column h3 {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--text-light);
}

.footer-column ul {
  list-style: none;
}

.footer-column ul li {
  margin-bottom: 0.8rem;
}

.footer-column ul li a {
  color: var(--text-muted);
  text-decoration: none;
  transition: var(--transition-normal);
}

.footer-column ul li a:hover {
  color: var(--accent-blue);
}

.admin-link {
  position: relative;
  font-weight: 500;
}

.admin-link::after {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: linear-gradient(90deg, var(--accent-purple), var(--accent-pink));
  margin-left: 6px;
  vertical-align: middle;
}

.admin-link:hover {
  color: var(--accent-pink) !important;
}

.app-links {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1.5rem;
}

.app-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  color: var(--text-light);
  text-decoration: none;
  transition: var(--transition-normal);
}

.app-link:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-3px);
}

.app-link img {
  width: 24px;
  height: 24px;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.footer-bottom p {
  color: var(--text-muted);
  font-size: 0.9rem;
}

.footer-bottom i {
  color: #ef4444;
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .hero-content, .cta-section {
    flex-direction: column;
  }

  .hero-text h1 {
    font-size: 2.5rem;
  }

  .tab-content.active {
    flex-direction: column;
  }

  .step {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .step:not(:last-child)::after {
    left: 30px;
    top: 60px;
    height: calc(100% - 60px);
  }
}

@media (max-width: 768px) {
  .hero-text h1 {
    font-size: 2rem;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .cta-content h2 {
    font-size: 2rem;
  }

  .hero-stats {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .stat {
    flex-direction: row;
    gap: 0.5rem;
    align-items: center;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }
}
