<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SleepSense AI | Smart Dashboard</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/build/three.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/three@0.132.2/examples/js/controls/OrbitControls.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    // Ensure jsPDF is properly initialized and globally available
    document.addEventListener('DOMContentLoaded', function() {
      if (typeof window.jspdf === 'undefined') {
        window.jspdf = {};
      }

      if (typeof window.jspdf.jsPDF === 'undefined' && typeof window.jsPDF !== 'undefined') {
        window.jspdf.jsPDF = window.jsPDF;
      }

      console.log('jsPDF initialization check:');
      console.log('window.jsPDF available:', typeof window.jsPDF !== 'undefined');
      console.log('window.jspdf.jsPDF available:', typeof window.jspdf !== 'undefined' && typeof window.jspdf.jsPDF !== 'undefined');
      console.log('html2pdf available:', typeof html2pdf !== 'undefined');
    });
  </script>
  <link rel="stylesheet" href="static/css/sleepsense_dashboard.css">
</head>
<body>
  <!-- Background Effects -->
  <div class="particles-container" id="particles"></div>
  <div class="brain-waves"></div>

  <!-- Mobile Menu Button -->
  <div class="mobile-menu-btn" id="mobileMenuBtn">
    <span></span>
  </div>

  <div class="dashboard-layout">
    <!-- Sidebar -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <svg class="sidebar-logo" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M9.5 2a2.5 2.5 0 1 1 0 5 2.5 2.5 0 0 1 0-5Z" stroke="#38bdf8" stroke-width="1.5"/>
          <path d="M14.5 2a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5Z" stroke="#818cf8" stroke-width="1.5"/>
          <path d="M3 10.5c0-1.1.9-2 2-2h14a2 2 0 0 1 2 2v1a2 2 0 0 1-2 2h-4l-4 2v-2H7a2 2 0 0 1-2-2v-1Z" stroke="#c084fc" stroke-width="1.5"/>
          <path d="M12 22c-1.5-1-2-3.5-2-6h4c0 2.5-.5 5-2 6Z" stroke="#38bdf8" stroke-width="1.5"/>
        </svg>
        <h1 class="sidebar-title">SleepSense AI</h1>
      </div>

      <div class="user-profile">
        <div class="user-avatar">
          <i class="fas fa-user"></i>
        </div>
        <h2 class="user-name" id="sidebar-username">User</h2>
        <p class="user-status"><span class="status-indicator"></span>Online</p>
        <div class="user-actions">
          <button class="action-btn" title="Settings">
            <i class="fas fa-cog"></i>
          </button>
          <button class="action-btn" title="Notifications">
            <i class="fas fa-bell"></i>
          </button>
          <button class="action-btn" title="Profile">
            <i class="fas fa-user-edit"></i>
          </button>
        </div>
      </div>

      <nav class="sidebar-nav">
        <ul>
          <li class="active">
            <a href="#dashboard">
              <i class="fas fa-chart-line"></i>
              <span>Dashboard</span>
            </a>
          </li>
          <li>
            <a href="#history">
              <i class="fas fa-history"></i>
              <span>History</span>
            </a>
          </li>
          <li>
            <a href="#insights">
              <i class="fas fa-lightbulb"></i>
              <span>Insights</span>
            </a>
          </li>
          <li>
            <a href="#reports">
              <i class="fas fa-file-alt"></i>
              <span>Reports</span>
            </a>
          </li>
        </ul>
      </nav>

      <div class="input-form">
        <h3 class="form-title">Input Your Data</h3>

        <div class="input-group">
          <label for="sleepHours">Sleep Hours (0-24)</label>
          <div class="input-with-icon">
            <i class="fas fa-moon"></i>
            <input type="number" id="sleepHours" min="0" max="24" step="0.5" placeholder="Enter sleep hours">
          </div>
        </div>

        <div class="input-group">
          <label for="sleepQuality">Sleep Quality (0-10)</label>
          <div class="input-with-icon">
            <i class="fas fa-star"></i>
            <input type="number" id="sleepQuality" min="0" max="10" step="0.5" placeholder="Rate sleep quality">
          </div>
        </div>

        <div class="input-group">
          <label for="currentMood">Current Mood</label>
          <div class="input-with-icon">
            <i class="fas fa-smile"></i>
            <select id="currentMood">
              <option value="happy">Happy 😊</option>
              <option value="calm">Calm 😌</option>
              <option value="neutral" selected>Neutral 😐</option>
              <option value="anxious">Anxious 😟</option>
              <option value="sad">Sad 😢</option>
              <option value="angry">Angry 😠</option>
              <option value="exhausted">Exhausted 😫</option>
            </select>
          </div>
        </div>

        <button id="analyzeBtn" class="analyze-btn">
          <i class="fas fa-brain"></i> Analyze Stress Level
        </button>
        <a href="sleepsense_home.html" class="logout-btn">
          <i class="fas fa-sign-out-alt"></i> Logout
        </a>
      </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
      <header class="content-header">
        <div class="header-left">
          <h2 class="welcome-text">Welcome, <span id="header-username">User</span></h2>
          <p class="date-time" id="currentDateTime"></p>
        </div>
        <div class="header-right">
          <div class="quick-stats">
            <div class="stat-item">
              <div class="stat-icon">
                <i class="fas fa-bed"></i>
              </div>
              <div class="stat-info">
                <span class="stat-value" id="avgSleepHours">0h</span>
                <span class="stat-label">Avg. Sleep</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">
                <i class="fas fa-star"></i>
              </div>
              <div class="stat-info">
                <span class="stat-value" id="avgSleepQuality">0</span>
                <span class="stat-label">Avg. Quality</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">
                <i class="fas fa-brain"></i>
              </div>
              <div class="stat-info">
                <span class="stat-value" id="avgStressLevel">0</span>
                <span class="stat-label">Avg. Stress</span>
              </div>
            </div>
          </div>
          <button id="exportFullReportBtn" class="export-report-btn" title="Export Full Report">
            <i class="fas fa-file-pdf"></i> Export Full Report
          </button>
        </div>

        <style>
          .export-report-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-left: 20px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
          }

          .export-report-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
          }

          .export-report-btn i {
            font-size: 1rem;
          }

          @media (max-width: 768px) {
            .header-right {
              flex-direction: column;
              align-items: flex-end;
              gap: 10px;
            }

            .export-report-btn {
              margin-left: 0;
              font-size: 0.8rem;
              padding: 6px 12px;
            }
          }
        </style>
      </header>

      <div class="dashboard-grid">
        <div class="dashboard-card visualization-card">
          <div class="card-header">
            <h3 class="card-title">Stress Visualization</h3>
            <div class="card-actions">
              <button class="card-action-btn" title="Refresh" id="refreshVisualization">
                <i class="fas fa-sync-alt"></i>
              </button>
              <button class="card-action-btn" title="Fullscreen" id="fullscreenVisualization">
                <i class="fas fa-expand"></i>
              </button>
              <a href="simple_dashboard.html" class="card-action-btn" title="Open in New Window" target="_blank">
                <i class="fas fa-external-link-alt"></i>
              </a>
            </div>
          </div>
          <div class="graph-container" style="min-height: 350px; height: 350px;">
            <div id="threeDGraph" style="width: 100%; height: 100%; position: absolute; top: 0; left: 0; z-index: 2;"></div>
            <div class="graph-fallback" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; flex-direction: column; gap: 1rem; text-align: center; padding: 2rem; z-index: 1;">
              <p>3D visualization may not be available in your browser.</p>
              <p>Click the <i class="fas fa-external-link-alt"></i> icon above to open the visualization in a new window.</p>
              <p>Or try analyzing your stress level to see the visualization.</p>
            </div>
          </div>
        </div>

        <div class="dashboard-card stress-meter-card">
          <div class="card-header">
            <h3 class="card-title">Current Stress Level</h3>
            <div class="card-actions">
              <button class="card-action-btn" title="Info" id="stressLevelInfo">
                <i class="fas fa-info-circle"></i>
              </button>
            </div>
          </div>
          <div class="stress-meter-container">
            <div class="stress-meter">
              <div class="stress-circle"></div>
              <div class="stress-level" id="stressLevel">0</div>
            </div>
            <div class="stress-scale">
              <div class="scale-item low">
                <span class="scale-dot"></span>
                <span class="scale-label">Low</span>
              </div>
              <div class="scale-item moderate">
                <span class="scale-dot"></span>
                <span class="scale-label">Moderate</span>
              </div>
              <div class="scale-item high">
                <span class="scale-dot"></span>
                <span class="scale-label">High</span>
              </div>
            </div>
          </div>
        </div>

        <div class="dashboard-card sleep-patterns-card">
          <div class="card-header">
            <h3 class="card-title">Sleep Patterns</h3>
            <div class="card-actions">
              <button class="card-action-btn" title="View History" id="viewSleepHistory">
                <i class="fas fa-history"></i>
              </button>
            </div>
          </div>
          <div id="sleepPatterns">
            <p class="placeholder-text">Analyze your stress level to see sleep pattern insights.</p>
          </div>
        </div>

        <div class="dashboard-card mood-trends-card">
          <div class="card-header">
            <h3 class="card-title">Mood Trends</h3>
            <div class="card-actions">
              <button class="card-action-btn" title="View Details" id="viewMoodDetails">
                <i class="fas fa-eye"></i>
              </button>
            </div>
          </div>
          <div class="mood-chart-container">
            <canvas id="moodChart"></canvas>
          </div>
        </div>
      </div>

      <div class="dashboard-card stress-result" id="stressResultCard" style="display: none;">
        <div class="card-header">
          <h3 class="card-title">Stress Analysis Results</h3>
          <div class="card-actions">
            <button class="card-action-btn" title="Export PDF" onclick="exportPDF()">
              <i class="fas fa-file-pdf"></i>
            </button>
            <button class="card-action-btn" title="Share" id="shareResults">
              <i class="fas fa-share-alt"></i>
            </button>
          </div>
        </div>
        <div class="result-content">
          <div class="stress-analysis">
            <p class="analysis-text" id="stressDescription">
              Enter your sleep data and click "Analyze Stress Level" to see your personalized stress analysis.
            </p>
            <div class="health-tip">
              <div class="tip-header">
                <i class="fas fa-lightbulb"></i>
                <h4 class="tip-title">Health Tip</h4>
              </div>
              <p id="healthTip">Your personalized health tip will appear here after analysis.</p>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>

  <script src="static/js/sleepsense_dashboard.js"></script>
</body>
</html>
