:root {
  --primary-dark: #0f172a;
  --primary-medium: #1e293b;
  --primary-light: #334155;
  --accent-blue: #38bdf8;
  --accent-purple: #818cf8;
  --accent-pink: #c084fc;
  --text-light: #e2e8f0;
  --text-muted: #94a3b8;
  --glass-bg: rgba(15, 23, 42, 0.7);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: rgba(0, 0, 0, 0.3);
  --transition-normal: all 0.3s ease;
  --success-green: #10b981;
  --warning-yellow: #f59e0b;
  --danger-red: #ef4444;
  --card-radius: 16px;
  --input-radius: 10px;
  --button-radius: 10px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-medium), var(--primary-light));
  color: var(--text-light);
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

/* Background Effects */
.particles-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: -1;
  pointer-events: none;
}

.particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
}

.brain-waves {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.1;
  z-index: -1;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1000 1000"><path d="M0,500 Q250,400 500,500 T1000,500" stroke="white" fill="none" stroke-width="2" /></svg>') repeat-x;
  background-size: 100% 100%;
  animation: wave 15s linear infinite;
  pointer-events: none;
}

@keyframes wave {
  0% { background-position: 0 0; }
  100% { background-position: 1000px 0; }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
  0% { opacity: 0.6; transform: scale(0.8); }
  50% { opacity: 1; transform: scale(1.2); }
  100% { opacity: 0.6; transform: scale(0.8); }
}

@keyframes float {
  0%, 100% { transform: translateY(0) translateX(0); }
  25% { transform: translateY(-20px) translateX(10px); }
  50% { transform: translateY(0) translateX(20px); }
  75% { transform: translateY(20px) translateX(10px); }
}

.dashboard-layout {
  display: grid;
  grid-template-columns: 320px 1fr;
  min-height: 100vh;
}

/* Sidebar */
.sidebar {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-right: 1px solid var(--glass-border);
  padding: 2rem;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 10;
  box-shadow: 5px 0 30px rgba(0, 0, 0, 0.2);
  overflow-y: auto;
  max-height: 100vh;
}

.sidebar-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.sidebar-logo {
  width: 40px;
  height: 40px;
  filter: drop-shadow(0 0 5px rgba(56, 189, 248, 0.5));
}

.sidebar-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple), var(--accent-pink));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.user-profile {
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--card-radius);
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid var(--glass-border);
  animation: fadeIn 0.5s ease-out forwards;
  text-align: center;
}

.user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin: 0 auto 1rem;
  box-shadow: 0 0 15px rgba(56, 189, 248, 0.3);
  color: white;
}

.user-name {
  font-weight: 600;
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}

.user-status {
  font-size: 0.9rem;
  color: var(--text-muted);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--success-green);
  animation: pulse 2s infinite;
}

.user-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
}

.action-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--glass-border);
  color: var(--text-light);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-normal);
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-3px);
}

/* Sidebar Navigation */
.sidebar-nav {
  margin-bottom: 2rem;
}

.sidebar-nav ul {
  list-style: none;
}

.sidebar-nav li {
  margin-bottom: 0.5rem;
}

.sidebar-nav a {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.8rem 1rem;
  border-radius: var(--button-radius);
  color: var(--text-light);
  text-decoration: none;
  transition: var(--transition-normal);
}

.sidebar-nav a:hover {
  background: rgba(255, 255, 255, 0.05);
}

.sidebar-nav li.active a {
  background: linear-gradient(90deg, rgba(56, 189, 248, 0.1), rgba(129, 140, 248, 0.1));
  border-left: 3px solid var(--accent-blue);
}

.sidebar-nav i {
  width: 20px;
  text-align: center;
  font-size: 1.1rem;
  color: var(--accent-blue);
}

/* Input Form */
.input-form {
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--card-radius);
  padding: 1.5rem;
  border: 1px solid var(--glass-border);
  animation: fadeIn 0.5s ease-out forwards;
  margin-top: auto;
}

.form-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--accent-blue);
}

.input-group {
  margin-bottom: 1.2rem;
}

.input-group label {
  display: block;
  font-size: 0.9rem;
  color: var(--text-muted);
  margin-bottom: 0.5rem;
}

.input-with-icon {
  position: relative;
}

.input-with-icon i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--accent-blue);
}

.input-with-icon input,
.input-with-icon select {
  width: 100%;
  padding: 0.8rem 0.8rem 0.8rem 2.5rem;
  border: 1px solid var(--glass-border);
  border-radius: var(--input-radius);
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-light);
  font-family: 'Poppins', sans-serif;
  font-size: 1rem;
  transition: var(--transition-normal);
}

.input-with-icon input:focus,
.input-with-icon select:focus {
  outline: none;
  border-color: var(--accent-blue);
  box-shadow: 0 0 0 2px rgba(56, 189, 248, 0.3);
  background: rgba(255, 255, 255, 0.08);
}

.input-with-icon select {
  appearance: none;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="6" viewBox="0 0 12 6"><path fill="%23e2e8f0" d="M0 0l6 6 6-6z"/></svg>');
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 12px;
  cursor: pointer;
}

.analyze-btn {
  width: 100%;
  padding: 0.8rem;
  border: none;
  border-radius: var(--button-radius);
  background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
  color: white;
  font-family: 'Poppins', sans-serif;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  margin-top: 1rem;
  position: relative;
  overflow: hidden;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.analyze-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
  z-index: -1;
  transition: var(--transition-normal);
}

.analyze-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 7px 20px rgba(56, 189, 248, 0.3);
}

.analyze-btn:hover::before {
  transform: scale(1.1);
  opacity: 0.9;
}

.logout-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-align: center;
  padding: 0.8rem;
  border: none;
  border-radius: var(--button-radius);
  background: linear-gradient(90deg, var(--danger-red), #fb7185);
  color: white;
  font-family: 'Poppins', sans-serif;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  margin-top: 1.5rem;
  text-decoration: none;
}

.logout-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 7px 20px rgba(239, 68, 68, 0.3);
}

/* Main Content */
.main-content {
  padding: 2rem;
  overflow-y: auto;
  max-height: 100vh;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: var(--card-radius);
  padding: 1.5rem;
  border: 1px solid var(--glass-border);
  box-shadow: 0 10px 30px var(--glass-shadow);
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.welcome-text {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.8rem;
  font-weight: 700;
}

.welcome-text span {
  background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple), var(--accent-pink));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.date-time {
  font-size: 0.9rem;
  color: var(--text-muted);
}

.header-right {
  display: flex;
  align-items: center;
}

.quick-stats {
  display: flex;
  gap: 1.5rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 0.8rem 1.2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--button-radius);
  border: 1px solid var(--glass-border);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(56, 189, 248, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--accent-blue);
  font-size: 1.2rem;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-weight: 700;
  font-size: 1.2rem;
  font-family: 'Montserrat', sans-serif;
}

.stat-label {
  font-size: 0.8rem;
  color: var(--text-muted);
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.dashboard-card {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: var(--card-radius);
  box-shadow: 0 10px 30px var(--glass-shadow);
  border: 1px solid var(--glass-border);
  padding: 1.5rem;
  animation: fadeIn 0.5s ease-out forwards;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.card-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--accent-purple);
}

.card-actions {
  display: flex;
  gap: 0.5rem;
}

.card-action-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--glass-border);
  color: var(--text-light);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-normal);
}

.card-action-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.visualization-card {
  grid-column: span 2;
  display: flex;
  flex-direction: column;
}

.graph-container {
  height: 350px;
  width: 100%;
  border-radius: 10px;
  overflow: hidden;
  background: rgba(15, 23, 42, 0.5);
  flex-grow: 1;
  position: relative;
  min-height: 350px;
  z-index: 1;
}

#threeDGraph {
  width: 100% !important;
  height: 100% !important;
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  z-index: 2;
}

#threeDGraph canvas {
  width: 100% !important;
  height: 100% !important;
  display: block;
  outline: none;
}

.stress-meter-card {
  display: flex;
  flex-direction: column;
}

.stress-meter-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
  gap: 2rem;
}

.stress-meter {
  width: 150px;
  height: 150px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stress-level {
  font-size: 3rem;
  font-weight: 700;
  font-family: 'Montserrat', sans-serif;
  z-index: 1;
}

.stress-circle {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: conic-gradient(var(--accent-blue) 0%, var(--accent-purple) 50%, var(--accent-pink) 100%);
  opacity: 0.2;
}

.stress-scale {
  display: flex;
  justify-content: space-between;
  width: 100%;
  max-width: 250px;
}

.scale-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.scale-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.scale-item.low .scale-dot {
  background: var(--success-green);
}

.scale-item.moderate .scale-dot {
  background: var(--warning-yellow);
}

.scale-item.high .scale-dot {
  background: var(--danger-red);
}

.scale-label {
  font-size: 0.8rem;
  color: var(--text-muted);
}

.sleep-patterns-card {
  overflow: auto;
}

.mood-trends-card {
  display: flex;
  flex-direction: column;
}

.mood-chart-container {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stress-result {
  margin-bottom: 2rem;
}

.result-content {
  display: flex;
  gap: 1.5rem;
}

.stress-analysis {
  flex: 1;
}

.analysis-text {
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.health-tip {
  background: rgba(16, 185, 129, 0.1);
  border-radius: 10px;
  padding: 1.5rem;
  margin-top: 1.5rem;
}

.tip-header {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin-bottom: 1rem;
}

.tip-header i {
  color: var(--success-green);
  font-size: 1.5rem;
}

.tip-title {
  font-weight: 600;
  color: var(--success-green);
  font-family: 'Montserrat', sans-serif;
}

.placeholder-text {
  color: var(--text-muted);
  text-align: center;
  padding: 2rem;
}

/* Mobile menu button */
.mobile-menu-btn {
  display: none;
  position: fixed;
  top: 1rem;
  left: 1rem;
  z-index: 100;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 8px;
  width: 40px;
  height: 40px;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.mobile-menu-btn span {
  display: block;
  width: 20px;
  height: 2px;
  background: var(--text-light);
  position: relative;
  transition: var(--transition-normal);
}

.mobile-menu-btn span::before,
.mobile-menu-btn span::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 2px;
  background: var(--text-light);
  transition: var(--transition-normal);
}

.mobile-menu-btn span::before {
  transform: translateY(-6px);
}

.mobile-menu-btn span::after {
  transform: translateY(6px);
}

.mobile-menu-btn.active span {
  background: transparent;
}

.mobile-menu-btn.active span::before {
  transform: rotate(45deg);
}

.mobile-menu-btn.active span::after {
  transform: rotate(-45deg);
}

/* Mobile styles */
@media (max-width: 1200px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .visualization-card {
    grid-column: span 1;
  }

  .quick-stats {
    display: none;
  }
}

@media (max-width: 1024px) {
  .mobile-menu-btn {
    display: flex;
  }

  .dashboard-layout {
    grid-template-columns: 1fr;
  }

  .sidebar {
    position: fixed;
    left: -320px;
    top: 0;
    bottom: 0;
    width: 320px;
    z-index: 50;
    transition: var(--transition-normal);
  }

  .sidebar.active {
    left: 0;
  }

  .main-content {
    padding-top: 4rem;
  }
}

@media (max-width: 768px) {
  .content-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .stress-meter {
    width: 120px;
    height: 120px;
  }

  .stress-level {
    font-size: 2.5rem;
  }
}
