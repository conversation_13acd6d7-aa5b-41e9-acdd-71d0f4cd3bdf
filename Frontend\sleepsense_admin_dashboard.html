<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SleepSense AI | Admin Dashboard</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <link rel="stylesheet" href="static/css/sleepsense_dashboard.css">
  <style>
    /* Admin-specific styles */
    .admin-card {
      background: var(--glass-bg);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border-radius: var(--card-radius);
      box-shadow: 0 10px 30px var(--glass-shadow);
      border: 1px solid var(--glass-border);
      padding: 1.5rem;
      animation: fadeIn 0.5s ease-out forwards;
      margin-bottom: 1.5rem;
    }

    .admin-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 1rem;
      overflow: hidden;
      border-radius: 8px;
    }

    .admin-table th, .admin-table td {
      padding: 1rem;
      text-align: left;
      border-bottom: 1px solid var(--glass-border);
    }

    .admin-table th {
      background: rgba(129, 140, 248, 0.2);
      font-weight: 600;
      color: var(--accent-blue);
    }

    .admin-table tr:hover {
      background: rgba(255, 255, 255, 0.05);
    }

    .masked {
      font-family: 'Courier New', Courier, monospace;
      letter-spacing: 2px;
    }

    .update-btn, .view-btn, .view-activity-btn {
      background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      font-family: 'Poppins', sans-serif;
      font-size: 0.8rem;
      font-weight: 600;
      cursor: pointer;
      transition: var(--transition-normal);
      margin-right: 0.5rem;
    }

    .update-btn:hover, .view-btn:hover, .view-activity-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 10px rgba(56, 189, 248, 0.3);
    }

    .view-activity-btn {
      background: linear-gradient(90deg, #10b981, #3b82f6);
    }

    .view-activity-btn:hover {
      box-shadow: 0 4px 10px rgba(16, 185, 129, 0.3);
    }

    .delete-btn {
      background: linear-gradient(90deg, var(--danger-red), #fb7185);
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      font-family: 'Poppins', sans-serif;
      font-size: 0.8rem;
      font-weight: 600;
      cursor: pointer;
      transition: var(--transition-normal);
    }

    .delete-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 10px rgba(239, 68, 68, 0.3);
    }

    .contact-card {
      background: rgba(255, 255, 255, 0.05);
      padding: 1rem;
      border-radius: 10px;
      border-left: 3px solid var(--accent-purple);
      margin-bottom: 1rem;
      animation: fadeIn 0.5s ease-out forwards;
    }

    .contact-card h4 {
      color: var(--accent-blue);
      margin-bottom: 0.5rem;
      font-weight: 600;
    }

    .contact-card p {
      color: var(--text-light);
      margin-bottom: 0.5rem;
    }

    .contact-card .email {
      color: var(--text-muted);
      font-size: 0.9rem;
    }

    /* Stats grid and chart styles removed */

    @media (max-width: 768px) {

      .admin-table th, .admin-table td {
        padding: 0.8rem 0.5rem;
        font-size: 0.9rem;
      }

      .update-btn, .delete-btn, .view-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.7rem;
      }
    }
  </style>
</head>
<body>
  <!-- Background Effects -->
  <div class="particles-container" id="particles"></div>
  <div class="brain-waves"></div>

  <!-- Mobile Menu Button -->
  <div class="mobile-menu-btn" id="mobileMenuBtn">
    <span></span>
  </div>

  <div class="dashboard-layout">
    <!-- Sidebar -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <svg class="sidebar-logo" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1ZM12 11.99H19C18.47 16.11 15.72 19.78 12 20.93V12H5V6.3L12 3.19V11.99Z" fill="#c084fc"/>
        </svg>
        <h1 class="sidebar-title">Admin Panel</h1>
      </div>

      <div class="user-profile">
        <div class="user-avatar">
          <i class="fas fa-user-shield"></i>
        </div>
        <h2 class="user-name" id="sidebar-username">Administrator</h2>
        <p class="user-status"><span class="status-indicator"></span>Online</p>
        <div class="user-actions">
          <button class="action-btn" title="Settings">
            <i class="fas fa-cog"></i>
          </button>
          <button class="action-btn" title="Notifications">
            <i class="fas fa-bell"></i>
          </button>
          <button class="action-btn" title="Profile">
            <i class="fas fa-user-edit"></i>
          </button>
        </div>
      </div>

      <nav class="sidebar-nav">
        <ul>
          <li class="active">
            <a href="#users">
              <i class="fas fa-users"></i>
              <span>User Management</span>
            </a>
          </li>
          <li>
            <a href="#contacts">
              <i class="fas fa-envelope"></i>
              <span>Contact Submissions</span>
            </a>
          </li>
        </ul>
      </nav>

      <a href="sleepsense_admin_login.html" class="logout-btn">
        <i class="fas fa-sign-out-alt"></i> Logout
      </a>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
      <header class="content-header">
        <div class="header-left">
          <h2 class="welcome-text">Welcome, <span id="header-username">Administrator</span></h2>
          <p class="date-time" id="currentDateTime"></p>
        </div>
        <div class="header-right">
          <div class="quick-stats">
            <div class="stat-item">
              <div class="stat-icon">
                <i class="fas fa-users"></i>
              </div>
              <div class="stat-info">
                <span class="stat-value" id="totalUsers">0</span>
                <span class="stat-label">Total Users</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">
                <i class="fas fa-envelope"></i>
              </div>
              <div class="stat-info">
                <span class="stat-value" id="totalContacts">0</span>
                <span class="stat-label">Messages</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">
                <i class="fas fa-chart-line"></i>
              </div>
              <div class="stat-info">
                <span class="stat-value" id="activeUsers">0</span>
                <span class="stat-label">Active Users</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- Main content starts with User Management -->
      <!-- System Overview section has been removed as requested -->

      <!-- User Management Section -->
      <section id="users" class="admin-card">
        <div class="card-header">
          <h3 class="card-title">User Management</h3>
        </div>
        <div id="user-list">
          <table class="admin-table">
            <thead>
              <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Password</th>
                <th>Login Date & Time</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody id="userTableBody">
              <!-- Populated by JS -->
            </tbody>
          </table>
        </div>
      </section>

      <!-- Contact Submissions Section -->
      <section id="contacts" class="admin-card">
        <div class="card-header">
          <h3 class="card-title">Real User Contact Submissions</h3>
          <div class="card-actions">
            <button onclick="refreshContacts()" class="refresh-btn" title="Refresh contacts">
              <i class="fas fa-sync-alt"></i>
            </button>
          </div>
        </div>

        <style>
          .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
          }

          .card-actions {
            display: flex;
            gap: 10px;
          }

          .refresh-btn {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: var(--text-muted);
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
          }

          .refresh-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: var(--text-light);
          }

          .refresh-btn.spinning i {
            animation: spin 1s linear infinite;
          }

          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        </style>
        <div id="contactContainer">
          <!-- Populated by JS -->
        </div>
      </section>
    </main>
  </div>

  <script>
    /**
     * SleepSense AI Admin Dashboard
     */

    // Global variables
    let users = [];
    let contacts = [];

    // API base URL
    const API_BASE_URL = 'http://127.0.0.1:5000/api';

    // Initialize dashboard when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
      console.log('✅ Admin Dashboard loaded');

      // Check admin authentication
      checkAdminAuth();

      // Initialize components
      initializeDashboard();

      // Load data
      loadUsers();
      loadContacts();

      // Set up navigation
      setupNavigation();

      // Update date/time
      updateDateTime();
      setInterval(updateDateTime, 1000);

      // Create background effects
      createParticles();
    });

    // Check if admin is authenticated
    function checkAdminAuth() {
      const adminToken = localStorage.getItem('adminToken');
      if (!adminToken || adminToken !== 'authenticated') {
        console.log('Admin not authenticated, redirecting to login');
        window.location.href = 'sleepsense_admin_login.html';
        return;
      }
    }

    // Initialize dashboard components
    function initializeDashboard() {
      console.log('Initializing admin dashboard...');
    }

    // Load users from backend API
    async function loadUsers() {
      try {
        console.log('Loading users from API...');
        const response = await fetch(`${API_BASE_URL}/admin/users`);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        users = await response.json();
        console.log('Users loaded:', users);

        displayUsers();
        updateUserStats();

      } catch (error) {
        console.error('Error loading users:', error);
        showError('Failed to load users. Please check if the backend server is running.');
      }
    }

    // Load contacts from backend API
    async function loadContacts() {
      try {
        console.log('Loading contacts from API...');
        const response = await fetch(`${API_BASE_URL}/admin/contacts`);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        contacts = await response.json();
        console.log('Contacts loaded:', contacts);

        displayContacts();
        updateContactStats();

      } catch (error) {
        console.error('Error loading contacts:', error);
        showError('Failed to load contacts. Please check if the backend server is running.');
      }
    }

    // Display users in the table
    function displayUsers() {
      const userTableBody = document.getElementById('userTableBody');

      if (!userTableBody) {
        console.error('User table body not found');
        return;
      }

      if (users.length === 0) {
        userTableBody.innerHTML = `
          <tr>
            <td colspan="5" style="text-align: center; color: var(--text-muted); padding: 2rem;">
              No users found. Users will appear here when they sign up.
            </td>
          </tr>
        `;
        return;
      }

      userTableBody.innerHTML = users.map(user => `
        <tr>
          <td>${escapeHtml(user.name)}</td>
          <td>${escapeHtml(user.email)}</td>
          <td class="masked">••••••••</td>
          <td>Not available</td>
          <td>
            <button class="view-btn" onclick="viewUser(${user.id})">
              <i class="fas fa-eye"></i> View
            </button>
            <button class="delete-btn" onclick="deleteUser(${user.id})">
              <i class="fas fa-trash"></i> Delete
            </button>
          </td>
        </tr>
      `).join('');
    }

    // Display contacts
    function displayContacts() {
      const contactContainer = document.getElementById('contactContainer');

      if (!contactContainer) {
        console.error('Contact container not found');
        return;
      }

      if (contacts.length === 0) {
        contactContainer.innerHTML = `
          <div style="text-align: center; color: var(--text-muted); padding: 2rem;">
            <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
            <p>No contact submissions yet.</p>
            <p>Contact forms will appear here when users submit them.</p>
          </div>
        `;
        return;
      }

      contactContainer.innerHTML = contacts.map((contact, index) => `
        <div class="contact-card">
          <h4>${escapeHtml(contact.name)}</h4>
          <p class="email">${escapeHtml(contact.email)}</p>
          <p><strong>Message:</strong> ${escapeHtml(contact.concern)}</p>
          <div style="margin-top: 1rem;">
            <button class="view-btn" onclick="viewContact(${index})">
              <i class="fas fa-eye"></i> View Details
            </button>
            <button class="update-btn" onclick="markAsRead(${index})">
              <i class="fas fa-check"></i> Mark as Read
            </button>
          </div>
        </div>
      `).join('');
    }

    // Update user statistics
    function updateUserStats() {
      const totalUsersElement = document.getElementById('totalUsers');
      const activeUsersElement = document.getElementById('activeUsers');

      if (totalUsersElement) {
        totalUsersElement.textContent = users.length;
      }

      if (activeUsersElement) {
        // For demo purposes, assume all users are active
        activeUsersElement.textContent = users.length;
      }
    }

    // Update contact statistics
    function updateContactStats() {
      const totalContactsElement = document.getElementById('totalContacts');

      if (totalContactsElement) {
        totalContactsElement.textContent = contacts.length;
      }
    }

    // Delete user function
    async function deleteUser(userId) {
      if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
        return;
      }

      try {
        const response = await fetch(`${API_BASE_URL}/admin/delete_user/${userId}`, {
          method: 'DELETE'
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('User deleted:', result);

        // Reload users
        await loadUsers();

        showSuccess('User deleted successfully!');

      } catch (error) {
        console.error('Error deleting user:', error);
        showError('Failed to delete user. Please try again.');
      }
    }

    // View user details
    function viewUser(userId) {
      const user = users.find(u => u.id === userId);
      if (user) {
        alert(`User Details:\n\nName: ${user.name}\nEmail: ${user.email}\nID: ${user.id}`);
      }
    }

    // View contact details
    function viewContact(index) {
      const contact = contacts[index];
      if (contact) {
        alert(`Contact Details:\n\nName: ${contact.name}\nEmail: ${contact.email}\nMessage: ${contact.concern}`);
      }
    }

    // Mark contact as read (placeholder function)
    function markAsRead(index) {
      showSuccess('Contact marked as read!');
    }

    // Refresh contacts function
    async function refreshContacts() {
      const refreshBtn = document.querySelector('.refresh-btn');
      if (refreshBtn) {
        refreshBtn.classList.add('spinning');
      }

      await loadContacts();

      setTimeout(() => {
        if (refreshBtn) {
          refreshBtn.classList.remove('spinning');
        }
        showSuccess('Contacts refreshed!');
      }, 1000);
    }

    // Setup navigation
    function setupNavigation() {
      const navLinks = document.querySelectorAll('.sidebar-nav a');

      navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
          e.preventDefault();

          // Remove active class from all nav items
          document.querySelectorAll('.sidebar-nav li').forEach(li => {
            li.classList.remove('active');
          });

          // Add active class to clicked nav item
          this.parentElement.classList.add('active');

          // Show corresponding section
          const targetId = this.getAttribute('href').substring(1);
          showSection(targetId);
        });
      });
    }

    // Show specific section
    function showSection(sectionId) {
      // Hide all sections
      document.querySelectorAll('.admin-card').forEach(section => {
        section.style.display = 'none';
      });

      // Show target section
      const targetSection = document.getElementById(sectionId);
      if (targetSection) {
        targetSection.style.display = 'block';
      }
    }

    // Update date and time
    function updateDateTime() {
      const dateTimeElement = document.getElementById('currentDateTime');
      if (dateTimeElement) {
        const now = new Date();
        const options = {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        };
        dateTimeElement.textContent = now.toLocaleDateString('en-US', options);
      }
    }

    // Utility function to escape HTML
    function escapeHtml(text) {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    }

    // Show success message
    function showSuccess(message) {
      showToast(message, 'success');
    }

    // Show error message
    function showError(message) {
      showToast(message, 'error');
    }

    // Show toast notification
    function showToast(message, type = 'info') {
      // Remove existing toast
      const existingToast = document.querySelector('.toast-notification');
      if (existingToast) {
        existingToast.remove();
      }

      // Create new toast
      const toast = document.createElement('div');
      toast.className = `toast-notification ${type}`;
      toast.innerHTML = `
        <div class="toast-content">
          <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
          <span>${message}</span>
        </div>
      `;

      // Add styles
      toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? 'var(--success-green)' : type === 'error' ? 'var(--danger-red)' : 'var(--accent-blue)'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        z-index: 10000;
        animation: slideInRight 0.3s ease-out;
        max-width: 300px;
      `;

      document.body.appendChild(toast);

      // Auto remove after 5 seconds
      setTimeout(() => {
        if (toast.parentNode) {
          toast.style.animation = 'slideOutRight 0.3s ease-out';
          setTimeout(() => {
            if (toast.parentNode) {
              toast.remove();
            }
          }, 300);
        }
      }, 5000);
    }

    // Create background particles
    function createParticles() {
      const particlesContainer = document.getElementById('particles');
      if (!particlesContainer) return;

      // Clear existing particles
      particlesContainer.innerHTML = '';

      const colors = [
        'rgba(56, 189, 248, 0.6)',  // Blue
        'rgba(129, 140, 248, 0.6)', // Purple
        'rgba(192, 132, 252, 0.6)'  // Pink
      ];

      // Create 20 particles
      for (let i = 0; i < 20; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';

        // Random size between 2px and 4px
        const size = Math.random() * 2 + 2;
        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;

        // Random position
        const posX = Math.random() * 100;
        const posY = Math.random() * 100;
        particle.style.left = `${posX}%`;
        particle.style.top = `${posY}%`;

        // Random color
        const colorIndex = Math.floor(Math.random() * colors.length);
        particle.style.backgroundColor = colors[colorIndex];
        particle.style.boxShadow = `0 0 ${size * 2}px ${colors[colorIndex]}`;

        // Random animation duration between 20s and 40s
        const duration = Math.random() * 20 + 20;
        particle.style.animation = `float ${duration}s ease-in-out infinite`;
        particle.style.animationDelay = `-${Math.random() * duration}s`;

        particlesContainer.appendChild(particle);
      }
    }
  </script>

  <style>
    /* Toast notification styles */
    @keyframes slideInRight {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    @keyframes slideOutRight {
      from {
        transform: translateX(0);
        opacity: 1;
      }
      to {
        transform: translateX(100%);
        opacity: 0;
      }
    }

    .toast-content {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .toast-content i {
      font-size: 1.2rem;
    }
  </style>
</body>
</html>