<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SleepSense AI | Admin Dashboard</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <link rel="stylesheet" href="static/css/sleepsense_dashboard.css">
  <style>
    /* Admin-specific styles */
    .admin-card {
      background: var(--glass-bg);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      border-radius: var(--card-radius);
      box-shadow: 0 10px 30px var(--glass-shadow);
      border: 1px solid var(--glass-border);
      padding: 1.5rem;
      animation: fadeIn 0.5s ease-out forwards;
      margin-bottom: 1.5rem;
    }

    .admin-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 1rem;
      overflow: hidden;
      border-radius: 8px;
    }

    .admin-table th, .admin-table td {
      padding: 1rem;
      text-align: left;
      border-bottom: 1px solid var(--glass-border);
    }

    .admin-table th {
      background: rgba(129, 140, 248, 0.2);
      font-weight: 600;
      color: var(--accent-blue);
    }

    .admin-table tr:hover {
      background: rgba(255, 255, 255, 0.05);
    }

    .masked {
      font-family: 'Courier New', Courier, monospace;
      letter-spacing: 2px;
    }

    .update-btn, .view-btn, .view-activity-btn {
      background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      font-family: 'Poppins', sans-serif;
      font-size: 0.8rem;
      font-weight: 600;
      cursor: pointer;
      transition: var(--transition-normal);
      margin-right: 0.5rem;
    }

    .update-btn:hover, .view-btn:hover, .view-activity-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 10px rgba(56, 189, 248, 0.3);
    }

    .view-activity-btn {
      background: linear-gradient(90deg, #10b981, #3b82f6);
    }

    .view-activity-btn:hover {
      box-shadow: 0 4px 10px rgba(16, 185, 129, 0.3);
    }

    .delete-btn {
      background: linear-gradient(90deg, var(--danger-red), #fb7185);
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      font-family: 'Poppins', sans-serif;
      font-size: 0.8rem;
      font-weight: 600;
      cursor: pointer;
      transition: var(--transition-normal);
    }

    .delete-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 10px rgba(239, 68, 68, 0.3);
    }

    .contact-card {
      background: rgba(255, 255, 255, 0.05);
      padding: 1rem;
      border-radius: 10px;
      border-left: 3px solid var(--accent-purple);
      margin-bottom: 1rem;
      animation: fadeIn 0.5s ease-out forwards;
    }

    .contact-card h4 {
      color: var(--accent-blue);
      margin-bottom: 0.5rem;
      font-weight: 600;
    }

    .contact-card p {
      color: var(--text-light);
      margin-bottom: 0.5rem;
    }

    .contact-card .email {
      color: var(--text-muted);
      font-size: 0.9rem;
    }

    /* Stats grid and chart styles removed */

    @media (max-width: 768px) {

      .admin-table th, .admin-table td {
        padding: 0.8rem 0.5rem;
        font-size: 0.9rem;
      }

      .update-btn, .delete-btn, .view-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.7rem;
      }
    }
  </style>
</head>
<body>
  <!-- Background Effects -->
  <div class="particles-container" id="particles"></div>
  <div class="brain-waves"></div>

  <!-- Mobile Menu Button -->
  <div class="mobile-menu-btn" id="mobileMenuBtn">
    <span></span>
  </div>

  <div class="dashboard-layout">
    <!-- Sidebar -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <svg class="sidebar-logo" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1ZM12 11.99H19C18.47 16.11 15.72 19.78 12 20.93V12H5V6.3L12 3.19V11.99Z" fill="#c084fc"/>
        </svg>
        <h1 class="sidebar-title">Admin Panel</h1>
      </div>

      <div class="user-profile">
        <div class="user-avatar">
          <i class="fas fa-user-shield"></i>
        </div>
        <h2 class="user-name" id="sidebar-username">Administrator</h2>
        <p class="user-status"><span class="status-indicator"></span>Online</p>
        <div class="user-actions">
          <button class="action-btn" title="Settings">
            <i class="fas fa-cog"></i>
          </button>
          <button class="action-btn" title="Notifications">
            <i class="fas fa-bell"></i>
          </button>
          <button class="action-btn" title="Profile">
            <i class="fas fa-user-edit"></i>
          </button>
        </div>
      </div>

      <nav class="sidebar-nav">
        <ul>
          <li class="active">
            <a href="#users">
              <i class="fas fa-users"></i>
              <span>User Management</span>
            </a>
          </li>
          <li>
            <a href="#contacts">
              <i class="fas fa-envelope"></i>
              <span>Contact Submissions</span>
            </a>
          </li>
        </ul>
      </nav>

      <a href="sleepsense_admin_login.html" class="logout-btn">
        <i class="fas fa-sign-out-alt"></i> Logout
      </a>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
      <header class="content-header">
        <div class="header-left">
          <h2 class="welcome-text">Welcome, <span id="header-username">Administrator</span></h2>
          <p class="date-time" id="currentDateTime"></p>
        </div>
        <div class="header-right">
          <div class="quick-stats">
            <div class="stat-item">
              <div class="stat-icon">
                <i class="fas fa-users"></i>
              </div>
              <div class="stat-info">
                <span class="stat-value" id="totalUsers">0</span>
                <span class="stat-label">Total Users</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">
                <i class="fas fa-envelope"></i>
              </div>
              <div class="stat-info">
                <span class="stat-value" id="totalContacts">0</span>
                <span class="stat-label">Messages</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">
                <i class="fas fa-chart-line"></i>
              </div>
              <div class="stat-info">
                <span class="stat-value" id="activeUsers">0</span>
                <span class="stat-label">Active Users</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <!-- Main content starts with User Management -->
      <!-- System Overview section has been removed as requested -->

      <!-- User Management Section -->
      <section id="users" class="admin-card">
        <div class="card-header">
          <h3 class="card-title">User Management</h3>
        </div>
        <div id="user-list">
          <table class="admin-table">
            <thead>
              <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Password</th>
                <th>Login Date & Time</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody id="userTableBody">
              <!-- Populated by JS -->
            </tbody>
          </table>
        </div>
      </section>

      <!-- Contact Submissions Section -->
      <section id="contacts" class="admin-card">
        <div class="card-header">
          <h3 class="card-title">Real User Contact Submissions</h3>
          <div class="card-actions">
            <button onclick="refreshContacts()" class="refresh-btn" title="Refresh contacts">
              <i class="fas fa-sync-alt"></i>
            </button>
          </div>
        </div>

        <style>
          .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
          }

          .card-actions {
            display: flex;
            gap: 10px;
          }

          .refresh-btn {
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: var(--text-muted);
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
          }

          .refresh-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            color: var(--text-light);
          }

          .refresh-btn.spinning i {
            animation: spin 1s linear infinite;
          }

          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        </style>
        <div id="contactContainer">
          <!-- Populated by JS -->
        </div>
      </section>
    </main>
  </div>

  <script>
    /**
     * SleepSense AI Admin Dashboard
     */
    // ... (all your script code here)
  </script>
</body>
</html>