from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
from werkzeug.security import generate_password_hash, check_password_hash
import jwt
import datetime

app = Flask(__name__)
CORS(app)

app.config["SQLALCHEMY_DATABASE_URI"] = "sqlite:///users.db"
app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = False
app.config["SECRET_KEY"] = "your_secret_key"

db = SQLAlchemy(app)

# User Model
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    password = db.Column(db.String(200), nullable=False)

with app.app_context():
    db.create_all()

# Admin Credentials
ADMIN_ID = "admin"
ADMIN_PASSWORD = "admin123"

# ========== ROUTES ==========

# User Signup
@app.route('/api/signup', methods=['POST'])
def signup():
    data = request.json
    name = data.get("name")
    email = data.get("email")
    password = data.get("password")

    if User.query.filter_by(email=email).first():
        return jsonify({"message": "Email already exists"}), 400

    hashed_password = generate_password_hash(password)
    new_user = User(name=name, email=email, password=hashed_password)
    db.session.add(new_user)
    db.session.commit()

    return jsonify({"message": "Signup successful"}), 201

# User Login
@app.route('/api/login', methods=['POST'])
def login():
    data = request.json
    email = data.get("email")
    password = data.get("password")

    user = User.query.filter_by(email=email).first()
    if user and check_password_hash(user.password, password):
        token = jwt.encode(
            {"email": user.email, "exp": datetime.datetime.utcnow() + datetime.timedelta(hours=1)},
            app.config["SECRET_KEY"],
            algorithm="HS256"
        )
        return jsonify({"token": token, "message": "Login successful", "user_id": user.id, "name": user.name })
    else:
        return jsonify({"error": "Invalid credentials"}), 401

# Admin Login
@app.route('/api/admin_login', methods=['POST'])
def admin_login():
    data = request.json
    username = data.get("username")
    password = data.get("password")

    if username == ADMIN_ID and password == ADMIN_PASSWORD:
        return jsonify({"message": "Login successful", "status": "success"}), 200
    else:
        return jsonify({"message": "Invalid credentials", "status": "error"}), 401

# Get all users (Admin only)
@app.route('/api/admin/users', methods=['GET'])
def get_users():
    users = User.query.all()
    users_list = [{"id": user.id, "name": user.name, "email": user.email} for user in users]
    return jsonify(users_list)

# Delete a user
@app.route('/api/admin/delete_user/<int:user_id>', methods=['DELETE'])
def delete_user(user_id):
    user = User.query.get(user_id)
    if user:
        db.session.delete(user)
        db.session.commit()
        return jsonify({"message": "User deleted successfully"}), 200
    return jsonify({"message": "User not found"}), 404

# Contact form submission
@app.route('/api/contact', methods=['POST'])
def save_contact():
    data = request.json
    name = data.get("name")
    email = data.get("email")
    concern = data.get("concern")

    if not name or not email or not concern:
        return jsonify({"message": "All fields are required"}), 400

    with open("contact_data.txt", "a") as f:
        f.write(f"{name} | {email} | {concern}\n")

    return jsonify({"message": "Form submitted successfully!"})

# Get contact form submissions (Admin)
@app.route('/api/admin/contacts', methods=['GET'])
def get_contact_data():
    contacts = []
    try:
        with open("contact_data.txt", "r") as file:
            for line in file.readlines():
                parts = line.strip().split(" | ")
                if len(parts) == 3:
                    contacts.append({
                        "name": parts[0],
                        "email": parts[1],
                        "concern": parts[2]
                    })
    except FileNotFoundError:
        return jsonify([])

    return jsonify(contacts)

if __name__ == "__main__":
    app.run(debug=True)
