:root {
  --primary-dark: #0f172a;
  --primary-medium: #1e293b;
  --primary-light: #334155;
  --accent-blue: #38bdf8;
  --accent-purple: #818cf8;
  --accent-pink: #c084fc;
  --text-light: #e2e8f0;
  --text-muted: #94a3b8;
  --glass-bg: rgba(15, 23, 42, 0.7);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: rgba(0, 0, 0, 0.3);
  --transition-normal: all 0.3s ease;
  --success-green: #10b981;
  --warning-yellow: #f59e0b;
  --danger-red: #ef4444;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-medium), var(--primary-light));
  color: var(--text-light);
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

/* Background Effects */
.particles-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: -1;
  pointer-events: none;
}

.particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
}

.brain-waves {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.1;
  z-index: -1;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1000 1000"><path d="M0,500 Q250,400 500,500 T1000,500" stroke="white" fill="none" stroke-width="2" /></svg>') repeat-x;
  background-size: 100% 100%;
  animation: wave 15s linear infinite;
  pointer-events: none;
}

/* Animations */
@keyframes wave {
  0% { background-position: 0 0; }
  100% { background-position: 1000px 0; }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
  0% { opacity: 0.6; transform: scale(0.8); }
  50% { opacity: 1; transform: scale(1.2); }
  100% { opacity: 0.6; transform: scale(0.8); }
}

@keyframes float {
  0%, 100% { transform: translateY(0) translateX(0); }
  25% { transform: translateY(-20px) translateX(10px); }
  50% { transform: translateY(0) translateX(20px); }
  75% { transform: translateY(20px) translateX(10px); }
}

/* Navbar Styles */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--glass-border);
  position: sticky;
  top: 0;
  z-index: 100;
}

.logo {
  display: flex;
  align-items: center;
}

.logo a {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  color: var(--text-light);
}

.brain-logo {
  filter: drop-shadow(0 0 5px rgba(56, 189, 248, 0.5));
}

.logo span {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  font-size: 1.5rem;
  background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple), var(--accent-pink));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.nav-list {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-list a {
  color: var(--text-light);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition-normal);
  position: relative;
}

.nav-list a::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
  transition: var(--transition-normal);
}

.nav-list a:hover {
  color: var(--accent-blue);
}

.nav-list a:hover::after {
  width: 100%;
}

.admin-btn {
  padding: 0.5rem 1.5rem;
  background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
  color: white;
  text-decoration: none;
  border-radius: 50px;
  font-weight: 600;
  transition: var(--transition-normal);
  box-shadow: 0 4px 15px rgba(56, 189, 248, 0.3);
}

.admin-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 7px 20px rgba(56, 189, 248, 0.5);
}

.burger {
  display: none;
  flex-direction: column;
  gap: 5px;
  cursor: pointer;
}

.burger .line {
  width: 25px;
  height: 3px;
  background-color: var(--text-light);
  border-radius: 3px;
  transition: var(--transition-normal);
}

/* Home Page Styles */
.firstSection {
  padding: 4rem 2rem;
  min-height: calc(100vh - 80px);
  display: flex;
  align-items: center;
}

.box-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  gap: 2rem;
}

.firstHalf {
  flex: 1;
  animation: fadeIn 1s ease-out;
}

.text-big {
  font-family: 'Montserrat', sans-serif;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple), var(--accent-pink));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.text-small {
  font-size: 1.1rem;
  line-height: 1.6;
  color: var(--text-muted);
  margin-bottom: 2rem;
}

.button {
  display: flex;
  gap: 1rem;
}

.btn {
  padding: 0.8rem 2rem;
  border: none;
  border-radius: 50px;
  background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
  color: white;
  font-family: 'Poppins', sans-serif;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-normal);
  box-shadow: 0 4px 15px rgba(56, 189, 248, 0.3);
}

.btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 7px 20px rgba(56, 189, 248, 0.5);
}

.hero-image {
  flex: 1;
  display: flex;
  justify-content: center;
  animation: fadeIn 1s ease-out 0.3s both;
}

.hero-image img {
  max-width: 100%;
  border-radius: 16px;
  box-shadow: 0 20px 50px var(--glass-shadow);
}

/* Section Styles */
.section, .section-left {
  padding: 4rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.section-left {
  flex-direction: row-reverse;
}

.parah {
  flex: 1;
}

.thumbnail {
  flex: 1;
  display: flex;
  justify-content: center;
}

.imgfluid {
  max-width: 100%;
  border-radius: 16px;
  box-shadow: 0 20px 50px var(--glass-shadow);
}

.stress-symptoms {
  list-style-position: inside;
  margin-top: 1rem;
}

.stress-symptoms li {
  margin-bottom: 0.5rem;
  position: relative;
  padding-left: 1.5rem;
}

.stress-symptoms li::before {
  content: '•';
  color: var(--accent-blue);
  position: absolute;
  left: 0;
}

/* Contact Section */
.contact {
  padding: 4rem 2rem;
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.text-center {
  text-align: center;
}

.contact-decoration {
  position: absolute;
  top: 2rem;
  left: -3rem;
  opacity: 0.2;
  z-index: -1;
}

.contact-decoration.right {
  left: auto;
  right: -3rem;
  top: auto;
  bottom: 2rem;
}

.form {
  margin-top: 2rem;
}

.form-input {
  width: 100%;
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid var(--glass-border);
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-light);
  font-family: 'Poppins', sans-serif;
  resize: vertical;
}

.form-input:focus {
  outline: none;
  border-color: var(--accent-blue);
  box-shadow: 0 0 0 2px rgba(56, 189, 248, 0.3);
}

/* Footer */
footer {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-top: 1px solid var(--glass-border);
  padding: 2rem;
  margin-top: 2rem;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  flex-wrap: wrap;
  gap: 1rem;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.footer-logo span {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  font-size: 1.2rem;
  background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple), var(--accent-pink));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.footer-links {
  display: flex;
  gap: 1.5rem;
}

.footer-links a {
  color: var(--text-muted);
  text-decoration: none;
  transition: var(--transition-normal);
}

.footer-links a:hover {
  color: var(--accent-blue);
}

.copyright {
  text-align: center;
  color: var(--text-muted);
  font-size: 0.9rem;
  margin-top: 1.5rem;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .box-main, .section, .section-left {
    flex-direction: column;
  }
  
  .nav-list {
    position: fixed;
    top: 80px;
    right: -100%;
    flex-direction: column;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    width: 70%;
    height: calc(100vh - 80px);
    padding: 2rem;
    transition: var(--transition-normal);
    z-index: 100;
  }
  
  .nav-list.active {
    right: 0;
  }
  
  .burger {
    display: flex;
  }
  
  .text-big {
    font-size: 2rem;
  }
}
