<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SleepSense AI | User Login</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700&family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-dark: #0f172a;
            --primary-medium: #1e293b;
            --primary-light: #334155;
            --accent-blue: #38bdf8;
            --accent-purple: #818cf8;
            --accent-pink: #c084fc;
            --text-light: #e2e8f0;
            --text-muted: #94a3b8;
            --glass-bg: rgba(15, 23, 42, 0.7);
            --glass-border: rgba(255, 255, 255, 0.1);
            --glass-shadow: rgba(0, 0, 0, 0.3);
            --transition-normal: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-medium), var(--primary-light));
            color: var(--text-light);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        /* Background Effects */
        .particles-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            border-radius: 50%;
            pointer-events: none;
        }

        .brain-waves {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.1;
            z-index: -1;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1000 1000"><path d="M0,500 Q250,400 500,500 T1000,500" stroke="white" fill="none" stroke-width="2" /></svg>') repeat-x;
            background-size: 100% 100%;
            animation: wave 15s linear infinite;
            pointer-events: none;
        }

        @keyframes wave {
            0% {
                background-position: 0 0;
            }
            100% {
                background-position: 1000px 0;
            }
        }

        .login-container {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            padding: 2.5rem;
            border-radius: 16px;
            box-shadow: 0 20px 50px var(--glass-shadow);
            border: 1px solid var(--glass-border);
            width: 90%;
            max-width: 400px;
            text-align: center;
            position: relative;
            z-index: 1;
            animation: fadeIn 1s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1.5rem;
            filter: drop-shadow(0 0 10px rgba(56, 189, 248, 0.5));
        }

        h2 {
            font-family: 'Montserrat', sans-serif;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple), var(--accent-pink));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        input {
            width: 100%;
            padding: 1rem;
            margin-bottom: 1.2rem;
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-light);
            font-family: 'Poppins', sans-serif;
            font-size: 1rem;
            transition: var(--transition-normal);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1) inset;
            text-align: center;
        }

        input:focus {
            outline: none;
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 2px rgba(56, 189, 248, 0.3), 0 4px 10px rgba(0, 0, 0, 0.1) inset;
            background: rgba(255, 255, 255, 0.08);
        }

        input::placeholder {
            color: var(--text-muted);
        }

        button {
            width: 100%;
            padding: 1rem;
            border: none;
            border-radius: 10px;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
            color: white;
            font-family: 'Poppins', sans-serif;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition-normal);
            box-shadow: 0 4px 15px rgba(56, 189, 248, 0.3);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
            z-index: -1;
            transition: var(--transition-normal);
        }

        button:hover {
            transform: translateY(-3px);
            box-shadow: 0 7px 20px rgba(56, 189, 248, 0.5);
        }

        button:hover::before {
            transform: scale(1.1);
            opacity: 0.9;
        }

        p {
            margin-top: 1.5rem;
            color: var(--text-muted);
            font-size: 0.9rem;
        }

        a {
            color: var(--accent-blue);
            text-decoration: none;
            transition: var(--transition-normal);
            position: relative;
        }

        a::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 1px;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
            transition: var(--transition-normal);
        }

        a:hover {
            color: var(--accent-purple);
        }

        a:hover::after {
            width: 100%;
        }
    </style>
</head>

<body>
    <!-- Background Effects -->
    <div class="particles-container" id="particles"></div>
    <div class="brain-waves"></div>

    <div class="login-container">
        <svg class="login-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM7.07 18.28C7.5 17.38 10.12 16.5 12 16.5C13.88 16.5 16.51 17.38 16.93 18.28C15.57 19.36 13.86 20 12 20C10.14 20 8.43 19.36 7.07 18.28ZM18.36 16.83C16.93 15.09 13.46 14.5 12 14.5C10.54 14.5 7.07 15.09 5.64 16.83C4.62 15.49 4 13.82 4 12C4 7.59 7.59 4 12 4C16.41 4 20 7.59 20 12C20 13.82 19.38 15.49 18.36 16.83ZM12 6C10.06 6 8.5 7.56 8.5 9.5C8.5 11.44 10.06 13 12 13C13.94 13 15.5 11.44 15.5 9.5C15.5 7.56 13.94 6 12 6ZM12 11C11.17 11 10.5 10.33 10.5 9.5C10.5 8.67 11.17 8 12 8C12.83 8 13.5 8.67 13.5 9.5C13.5 10.33 12.83 11 12 11Z" fill="#38bdf8"/>
        </svg>
        <h2>User Login</h2>
        <form id="loginForm">
            <input type="email" id="email" placeholder="Email" required>
            <input type="password" id="password" placeholder="Password" required>
            <button type="submit">Login</button>
            <p>Don't have an account? <a href="sleepsense_signup.html">Sign Up</a></p>
            <p><a href="sleepsense_home.html">Return to Home</a></p>
        </form>
    </div>

    <script>
        /**
         * SleepSense AI Login Page
         */
        document.addEventListener('DOMContentLoaded', function () {
            console.log('✅ Login page script loaded');

            // Create background particles
            createParticles();

            // Initialize login form
            const loginForm = document.getElementById('loginForm');

            if (loginForm) {
                loginForm.addEventListener('submit', function (event) {
                    event.preventDefault();  // Prevent page reload

                    const email = document.getElementById('email').value.trim();
                    const password = document.getElementById('password').value.trim();

                    // Simple validation
                    if (!email || !password) {
                        showToast('Please enter both email and password');
                        return;
                    }

                    // Get users from localStorage
                    const users = JSON.parse(localStorage.getItem('users') || '[]');

                    // Find user with matching email and password
                    const user = users.find(u => u.email === email && u.password === password);

                    if (user) {
                        // Store user info in localStorage
                        localStorage.setItem('currentUser', JSON.stringify({ name: user.name, email: user.email }));
                        localStorage.setItem('user_name', user.name);
                        localStorage.setItem('isLoggedIn', 'true');

                        // Show success message
                        showToast('Login successful!');

                        // Redirect to dashboard after a short delay
                        setTimeout(() => {
                            window.location.href = 'sleepsense_dashboard.html';
                        }, 1500);
                    } else {
                        // Try demo login
                        if (email === '<EMAIL>' && password === 'password') {
                            localStorage.setItem('currentUser', JSON.stringify({ name: 'Demo User', email: '<EMAIL>' }));
                            localStorage.setItem('user_name', 'Demo User');
                            localStorage.setItem('isLoggedIn', 'true');

                            showToast('Demo login successful!');

                            setTimeout(() => {
                                window.location.href = 'sleepsense_dashboard.html';
                            }, 1500);
                        } else {
                            showToast('Invalid email or password');
                        }
                    }
                });
            } else {
                console.error('❌ ERROR: loginForm not found! Make sure id="loginForm" exists in sleepsense_login.html');
            }
        });

        // Helper function to show toast notification
        function showToast(message) {
            // Remove existing toast if any
            const existingToast = document.querySelector('.toast-notification');
            if (existingToast) {
                existingToast.remove();
            }

            // Create toast element
            const toast = document.createElement('div');
            toast.className = 'toast-notification';
            toast.textContent = message;

            // Add toast to body
            document.body.appendChild(toast);

            // Add CSS for toast
            const style = document.createElement('style');
            style.textContent = `
                .toast-notification {
                    position: fixed;
                    bottom: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    background: var(--glass-bg, rgba(15, 23, 42, 0.7));
                    color: var(--text-light, #e2e8f0);
                    padding: 12px 24px;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                    z-index: 1000;
                    font-size: 0.9rem;
                    backdrop-filter: blur(10px);
                    border: 1px solid var(--glass-border, rgba(255, 255, 255, 0.1));
                    animation: toastIn 0.3s ease-out forwards, toastOut 0.3s ease-in forwards 3s;
                }

                @keyframes toastIn {
                    from { opacity: 0; transform: translate(-50%, 20px); }
                    to { opacity: 1; transform: translate(-50%, 0); }
                }

                @keyframes toastOut {
                    from { opacity: 1; transform: translate(-50%, 0); }
                    to { opacity: 0; transform: translate(-50%, 20px); }
                }
            `;
            document.head.appendChild(style);

            // Remove toast after animation
            setTimeout(() => {
                toast.remove();
                style.remove();
            }, 3500);
        }

        // Create background particles
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            if (!particlesContainer) return;

            // Clear existing particles
            particlesContainer.innerHTML = '';

            const colors = [
                'rgba(56, 189, 248, 0.6)',  // Blue
                'rgba(129, 140, 248, 0.6)', // Purple
                'rgba(192, 132, 252, 0.6)'  // Pink
            ];

            // Create 30 particles (reduced for better performance)
            for (let i = 0; i < 30; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';

                // Random size between 2px and 6px
                const size = Math.random() * 4 + 2;
                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;

                // Random position
                const posX = Math.random() * 100;
                const posY = Math.random() * 100;
                particle.style.left = `${posX}%`;
                particle.style.top = `${posY}%`;

                // Random color
                const colorIndex = Math.floor(Math.random() * colors.length);
                particle.style.backgroundColor = colors[colorIndex];
                particle.style.boxShadow = `0 0 ${size * 2}px ${colors[colorIndex]}`;

                // Random animation duration between 15s and 30s
                const duration = Math.random() * 15 + 15;
                particle.style.animation = `float ${duration}s ease-in-out infinite`;
                particle.style.animationDelay = `-${Math.random() * duration}s`;

                particlesContainer.appendChild(particle);
            }
        }
    </script>
</body>

</html>
