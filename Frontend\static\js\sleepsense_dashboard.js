document.addEventListener('DOMContentLoaded', () => {
  console.log('✅ New Dashboard Loaded');

  // DOM Elements
  const sleepHoursInput = document.getElementById('sleepHours');
  const sleepQualityInput = document.getElementById('sleepQuality');
  const currentMoodSelect = document.getElementById('currentMood');
  const analyzeBtn = document.getElementById('analyzeBtn');
  const stressResultCard = document.getElementById('stressResultCard');
  const stressLevel = document.getElementById('stressLevel');
  const stressDescription = document.getElementById('stressDescription');
  const healthTip = document.getElementById('healthTip');
  const sleepPatterns = document.getElementById('sleepPatterns');
  const currentDateTime = document.getElementById('currentDateTime');
  const sidebarUsername = document.getElementById('sidebar-username');
  const headerUsername = document.getElementById('header-username');
  const mobileMenuBtn = document.getElementById('mobileMenuBtn');
  const sidebar = document.querySelector('.sidebar');
  const avgSleepHours = document.getElementById('avgSleepHours');
  const avgSleepQuality = document.getElementById('avgSleepQuality');
  const avgStressLevel = document.getElementById('avgStressLevel');

  // Mock data for history (in a real app, this would come from a database)
  const mockHistory = [
    { sleepHours: 7.5, sleepQuality: 8, mood: 'happy', stressLevel: 3.2 },
    { sleepHours: 6, sleepQuality: 5, mood: 'neutral', stressLevel: 5.8 },
    { sleepHours: 8, sleepQuality: 7, mood: 'calm', stressLevel: 2.5 },
    { sleepHours: 5.5, sleepQuality: 4, mood: 'anxious', stressLevel: 7.3 },
    { sleepHours: 7, sleepQuality: 6, mood: 'neutral', stressLevel: 4.6 }
  ];

  // Get username from localStorage
  // Try multiple possible keys for username
  let username = localStorage.getItem('user_name');

  // If not found, try currentUser object
  if (!username || username === 'null' || username === 'undefined') {
    try {
      const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
      if (currentUser && currentUser.name) {
        username = currentUser.name;
        // Also update the user_name key for consistency
        localStorage.setItem('user_name', username);
      }
    } catch (e) {
      console.error('Error parsing currentUser from localStorage:', e);
    }
  }

  // If still not found, use Guest
  if (!username || username === 'null' || username === 'undefined') {
    username = 'Guest';
  }

  // Update username display in both places
  if (sidebarUsername) sidebarUsername.textContent = username;
  if (headerUsername) headerUsername.textContent = username;

  console.log('Username set to:', username);

  // Set average stats from mock history
  if (avgSleepHours && avgSleepQuality && avgStressLevel) {
    updateAverageStats();
  }

  // Mobile menu toggle
  if (mobileMenuBtn && sidebar) {
    mobileMenuBtn.addEventListener('click', () => {
      mobileMenuBtn.classList.toggle('active');
      sidebar.classList.toggle('active');
    });

    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', (e) => {
      if (window.innerWidth <= 1024 &&
          sidebar.classList.contains('active') &&
          !sidebar.contains(e.target) &&
          e.target !== mobileMenuBtn &&
          !mobileMenuBtn.contains(e.target)) {
        sidebar.classList.remove('active');
        mobileMenuBtn.classList.remove('active');
      }
    });
  }

  // Set current date and time
  if (currentDateTime) {
    updateDateTime();
    setInterval(updateDateTime, 60000); // Update every minute
  }

  // Initialize Three.js variables
  let scene, camera, renderer, controls;
  let sleepHoursBar, sleepQualityBar, moodBar, stressBar;
  let valueLabels = [];

  // Mood values mapping
  const moodValues = {
    'happy': 9,
    'calm': 8,
    'neutral': 6,
    'anxious': 4,
    'sad': 3,
    'angry': 2,
    'exhausted': 1
  };

  // Mood colors mapping
  const moodColors = {
    'happy': 0x10b981, // Green
    'calm': 0x60a5fa, // Light blue
    'neutral': 0x818cf8, // Purple
    'anxious': 0xfbbf24, // Orange
    'sad': 0x93c5fd, // Blue
    'angry': 0xef4444, // Red
    'exhausted': 0x6b7280 // Gray
  };

  // Initialize the graph when the window is fully loaded
  window.addEventListener('load', () => {
    console.log("Window loaded, initializing graph");
    initGraph();
    console.log("3D Graph initialized");

    // Add a window resize event listener
    window.addEventListener('resize', () => {
      console.log("Window resized, updating graph");
      onWindowResize();
    });

    // Force a resize after a short delay to ensure proper rendering
    setTimeout(() => {
      onWindowResize();
      console.log("Forced graph resize for better rendering");
    }, 500);
  });

  // Initialize mood chart when the window is fully loaded
  window.addEventListener('load', () => {
    initMoodChart();
    console.log("Mood chart initialized");
  });

  // Event listeners
  if (analyzeBtn) {
    analyzeBtn.addEventListener('click', analyzeStress);
  } else {
    console.error('Analyze button not found in the DOM');
  }

  // Button event listeners - Add null checks to prevent errors
  const refreshVisualizationBtn = document.getElementById('refreshVisualization');
  if (refreshVisualizationBtn) {
    refreshVisualizationBtn.addEventListener('click', () => {
      // Get current values from inputs
      let sleepHours = 8;
      let sleepQuality = 7;
      let currentMood = "neutral";

      // If user has entered values, use those instead of defaults
      if (sleepHoursInput && sleepHoursInput.value.trim()) {
        sleepHours = parseFloat(sleepHoursInput.value);
        // Ensure valid range
        sleepHours = Math.min(24, Math.max(0, sleepHours));
      }

      if (sleepQualityInput && sleepQualityInput.value.trim()) {
        sleepQuality = parseFloat(sleepQualityInput.value);
        // Ensure valid range
        sleepQuality = Math.min(10, Math.max(0, sleepQuality));
      }

      if (currentMoodSelect) {
        currentMood = currentMoodSelect.value;
      }
      const moodValue = moodValues[currentMood] || moodValues["neutral"];

      // Calculate stress score
      let sleepHoursFactor;
      if (sleepHours >= 7 && sleepHours <= 9) {
        sleepHoursFactor = 0;
      } else if (sleepHours < 7) {
        sleepHoursFactor = ((7 - sleepHours) / 7) * 4;
      } else {
        sleepHoursFactor = ((sleepHours - 9) / 15) * 2;
      }

      const sleepQualityFactor = ((10 - sleepQuality) / 10) * 6;
      const moodFactor = ((10 - moodValue) / 10) * 4;
      const stressScore = Math.min(10, Math.max(0, sleepQualityFactor + sleepHoursFactor + moodFactor));

      // Refresh the visualization with current values
      updateBars(sleepHours, sleepQuality, moodValue, stressScore);
      showToast("Visualization refreshed with current values");
    });
  }

  const fullscreenVisualizationBtn = document.getElementById('fullscreenVisualization');
  if (fullscreenVisualizationBtn) {
    fullscreenVisualizationBtn.addEventListener('click', () => {
      const container = document.querySelector('.visualization-card');
      if (container) {
        if (container.requestFullscreen) {
          container.requestFullscreen();
        } else if (container.webkitRequestFullscreen) {
          container.webkitRequestFullscreen();
        } else if (container.msRequestFullscreen) {
          container.msRequestFullscreen();
        }
      }
    });
  }

  const stressLevelInfoBtn = document.getElementById('stressLevelInfo');
  if (stressLevelInfoBtn) {
    stressLevelInfoBtn.addEventListener('click', () => {
      showToast("Stress levels: 0-3 (Low), 4-6 (Moderate), 7-10 (High)");
    });
  }

  const viewSleepHistoryBtn = document.getElementById('viewSleepHistory');
  if (viewSleepHistoryBtn) {
    viewSleepHistoryBtn.addEventListener('click', () => {
      showToast("Sleep history feature coming soon!");
    });
  }

  const viewMoodDetailsBtn = document.getElementById('viewMoodDetails');
  if (viewMoodDetailsBtn) {
    viewMoodDetailsBtn.addEventListener('click', () => {
      showToast("Detailed mood analysis coming soon!");
    });
  }

  const shareResultsBtn = document.getElementById('shareResults');
  if (shareResultsBtn) {
    shareResultsBtn.addEventListener('click', () => {
      showToast("Share functionality coming soon!");
    });
  }

  function updateDateTime() {
    if (!currentDateTime) return;

    const now = new Date();
    const options = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    currentDateTime.textContent = now.toLocaleDateString('en-US', options);
  }

  function updateAverageStats() {
    // Check if elements exist
    if (!avgSleepHours || !avgSleepQuality || !avgStressLevel) {
      console.warn('Average stats elements not found in the DOM');
      return;
    }

    // Calculate averages from mock history
    const totalEntries = mockHistory.length;

    if (totalEntries > 0) {
      const totalSleepHours = mockHistory.reduce((sum, entry) => sum + entry.sleepHours, 0);
      const totalSleepQuality = mockHistory.reduce((sum, entry) => sum + entry.sleepQuality, 0);
      const totalStressLevel = mockHistory.reduce((sum, entry) => sum + entry.stressLevel, 0);

      avgSleepHours.textContent = (totalSleepHours / totalEntries).toFixed(1) + 'h';
      avgSleepQuality.textContent = (totalSleepQuality / totalEntries).toFixed(1);
      avgStressLevel.textContent = (totalStressLevel / totalEntries).toFixed(1);
    }
  }

  function analyzeStress() {
    // Check if required elements exist
    if (!sleepHoursInput || !sleepQualityInput || !currentMoodSelect || !stressResultCard || !stressLevel) {
      console.error('Required DOM elements for stress analysis not found');
      showToast("Error: Some required elements are missing. Please refresh the page.");
      return;
    }

    // Check for empty inputs
    if (!sleepHoursInput.value.trim() || !sleepQualityInput.value.trim()) {
      // Add shake animation to empty inputs
      if (!sleepHoursInput.value.trim()) {
        shakeElement(sleepHoursInput);
      }
      if (!sleepQualityInput.value.trim()) {
        shakeElement(sleepQualityInput);
      }

      // Show error message
      showToast("Please enter both sleep hours and quality.");
      return;
    }

    const sleepHours = parseFloat(sleepHoursInput.value);
    const sleepQuality = parseFloat(sleepQualityInput.value);
    const currentMood = currentMoodSelect.value;

    // Validate inputs are numbers
    if (isNaN(sleepHours) || isNaN(sleepQuality)) {
      showToast("Please enter valid numeric values.");
      return;
    }

    // Enforce limits
    const clampedSleepHours = Math.min(24, Math.max(0, sleepHours));
    const clampedSleepQuality = Math.min(10, Math.max(0, sleepQuality));

    // Show message if values were clamped
    if (clampedSleepHours !== sleepHours || clampedSleepQuality !== sleepQuality) {
      showToast("Some values were adjusted to be within valid ranges.");
    }

    // Update input fields with clamped values
    sleepHoursInput.value = clampedSleepHours;
    sleepQualityInput.value = clampedSleepQuality;

    // Calculate stress score with mood factor
    let sleepHoursFactor;
    if (clampedSleepHours >= 7 && clampedSleepHours <= 9) {
      sleepHoursFactor = 0;
    } else if (clampedSleepHours < 7) {
      sleepHoursFactor = ((7 - clampedSleepHours) / 7) * 4;
    } else {
      sleepHoursFactor = ((clampedSleepHours - 9) / 15) * 2;
    }

    const sleepQualityFactor = ((10 - clampedSleepQuality) / 10) * 6;

    // Add mood factor to stress calculation
    const moodValue = moodValues[currentMood] || 6; // Default to neutral if not found
    const moodFactor = ((10 - moodValue) / 10) * 4; // Mood affects stress by up to 4 points

    const stressScore = Math.min(10, Math.max(0, sleepQualityFactor + sleepHoursFactor + moodFactor));

    // Update 3D graph with stress score
    updateBars(clampedSleepHours, clampedSleepQuality, moodValue, stressScore);

    // Update stress level display
    stressLevel.textContent = stressScore.toFixed(1);

    // Update stress circle color
    updateStressCircle(stressScore);

    // Generate stress description and tip
    generateStressDescription(stressScore, clampedSleepHours, clampedSleepQuality, currentMood);

    // Update sleep patterns
    updateSleepPatterns(clampedSleepHours, clampedSleepQuality, currentMood);

    // Show results card
    stressResultCard.style.display = 'block';

    // Add to mock history
    mockHistory.push({
      sleepHours: clampedSleepHours,
      sleepQuality: clampedSleepQuality,
      mood: currentMood,
      stressLevel: stressScore
    });

    // Update average stats
    updateAverageStats();

    // Update mood chart
    updateMoodChart();

    // Show success message
    showToast("Analysis complete!");
  }

  function updateStressCircle(stressScore) {
    const stressCircle = document.querySelector('.stress-circle');
    let color;

    if (stressScore >= 7) {
      color = 'var(--danger-red)';
      stressLevel.style.color = 'var(--danger-red)';
    } else if (stressScore >= 4) {
      color = 'var(--warning-yellow)';
      stressLevel.style.color = 'var(--warning-yellow)';
    } else {
      color = 'var(--success-green)';
      stressLevel.style.color = 'var(--success-green)';
    }

    stressCircle.style.background = color;
    stressCircle.style.opacity = '0.2';

    // Add pulse animation
    stressCircle.style.animation = 'pulse 2s infinite';
  }

  function initGraph() {
    try {
      // Get container
      const container = document.getElementById('threeDGraph');
      if (!container) {
        console.error("Could not find graph container element");
        return;
      }

      // Clear any existing content
      container.innerHTML = '';

      // Create scene
      scene = new THREE.Scene();
      scene.background = new THREE.Color(0x0f172a); // Dark blue background

      // Get container dimensions
      // Use parent container dimensions if the container itself has no dimensions yet
      const parentContainer = container.parentElement;
      let width = container.clientWidth || parentContainer.clientWidth || 500;
      let height = container.clientHeight || parentContainer.clientHeight || 350;

      // Ensure minimum dimensions
      width = Math.max(width, 300);
      height = Math.max(height, 300);

      console.log(`Graph container dimensions: ${width}x${height}`);

      // Create camera
      camera = new THREE.PerspectiveCamera(45, width / height, 0.1, 1000);
      camera.position.set(12, 12, 12);
      camera.lookAt(0, 0, 0);

      // Create renderer with better settings for compatibility
      renderer = new THREE.WebGLRenderer({
        antialias: true,
        alpha: true,
        preserveDrawingBuffer: true
      });
      renderer.setSize(width, height);
      renderer.setPixelRatio(window.devicePixelRatio);

      // Add the renderer to the container
      container.appendChild(renderer.domElement);

      // Apply styles directly to the canvas for better visibility
      const canvas = renderer.domElement;
      canvas.style.width = '100%';
      canvas.style.height = '100%';
      canvas.style.display = 'block';
      canvas.style.position = 'absolute';
      canvas.style.top = '0';
      canvas.style.left = '0';

      // Hide fallback message if graph is successfully initialized
      const fallbackMessage = document.querySelector('.graph-fallback');
      if (fallbackMessage) {
        fallbackMessage.style.display = 'none';
      }

      console.log("Three.js renderer created successfully");

      // Add orbit controls
      controls = new THREE.OrbitControls(camera, renderer.domElement);
      controls.enableDamping = true;
      controls.dampingFactor = 0.05;

      // Add lights
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
      scene.add(ambientLight);

      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
      directionalLight.position.set(10, 20, 10);
      scene.add(directionalLight);

      // Add grid
      const gridHelper = new THREE.GridHelper(20, 20, 0x334155, 0x1e293b);
      scene.add(gridHelper);

      // Add floor
      const floorGeometry = new THREE.PlaneGeometry(20, 20);
      const floorMaterial = new THREE.MeshBasicMaterial({
        color: 0x0f172a,
        side: THREE.DoubleSide
      });
      const floor = new THREE.Mesh(floorGeometry, floorMaterial);
      floor.rotation.x = -Math.PI / 2;
      floor.position.y = -0.01;
      scene.add(floor);

      // Add axis labels
      addAxisLabel('Sleep Hours', new THREE.Vector3(-6, 0, -6), 0x38bdf8);
      addAxisLabel('Sleep Quality', new THREE.Vector3(-2, 0, -6), 0x6366f1);
      addAxisLabel('Mood', new THREE.Vector3(2, 0, -6), 0xa855f7);
      addAxisLabel('Stress Level', new THREE.Vector3(6, 0, -6), 0xef4444);

      // Create initial bars with default values
      const initialMoodValue = moodValues["neutral"];
      updateBars(8, 7, initialMoodValue, 5);

      // Start animation loop
      animate();

      // Handle window resize
      window.addEventListener('resize', onWindowResize);

    } catch (error) {
      console.error("Error initializing graph:", error);

      // Show fallback message if there's an error
      const fallbackMessage = document.querySelector('.graph-fallback');
      if (fallbackMessage) {
        fallbackMessage.style.display = 'flex';
      }
    }
  }

  function updateBars(sleepHours, sleepQuality, moodValue, stressScore) {
    // Calculate stress score if not provided
    if (stressScore === undefined) {
      let sleepHoursFactor;
      if (sleepHours >= 7 && sleepHours <= 9) {
        sleepHoursFactor = 0;
      } else if (sleepHours < 7) {
        sleepHoursFactor = ((7 - sleepHours) / 7) * 4;
      } else {
        sleepHoursFactor = ((sleepHours - 9) / 15) * 2;
      }

      const sleepQualityFactor = ((10 - sleepQuality) / 10) * 6;
      const moodFactor = ((10 - moodValue) / 10) * 4;
      stressScore = Math.min(10, Math.max(0, sleepQualityFactor + sleepHoursFactor + moodFactor));
    }

    // Remove existing bars
    if (sleepHoursBar) scene.remove(sleepHoursBar);
    if (sleepQualityBar) scene.remove(sleepQualityBar);
    if (moodBar) scene.remove(moodBar);
    if (stressBar) scene.remove(stressBar);

    // Remove existing value labels
    valueLabels.forEach(label => scene.remove(label));
    valueLabels = [];

    // Determine stress color
    let stressColor;
    if (stressScore >= 7) {
      stressColor = 0xef4444; // Red for high stress
    } else if (stressScore >= 4) {
      stressColor = 0xf59e0b; // Yellow for moderate stress
    } else {
      stressColor = 0x10b981; // Green for low stress
    }

    // Get mood color
    const currentMood = currentMoodSelect.value;
    const moodColor = moodColors[currentMood] || 0x818cf8;

    // Create bars
    sleepHoursBar = createBar(-6, sleepHours, 0, 2, 2, 0x38bdf8);
    sleepQualityBar = createBar(-2, sleepQuality, 0, 2, 2, 0x6366f1);
    moodBar = createBar(2, moodValue, 0, 2, 2, moodColor);
    stressBar = createBar(6, stressScore, 0, 2, 2, stressColor);

    // Add bars to scene
    scene.add(sleepHoursBar);
    scene.add(sleepQualityBar);
    scene.add(moodBar);
    scene.add(stressBar);

    // Add value labels
    valueLabels.push(addValueLabel(`${sleepHours}h`, new THREE.Vector3(-6, sleepHours + 1, 0)));
    valueLabels.push(addValueLabel(`${sleepQuality}/10`, new THREE.Vector3(-2, sleepQuality + 1, 0)));
    valueLabels.push(addValueLabel(`${moodValue}/10`, new THREE.Vector3(2, moodValue + 1, 0)));
    valueLabels.push(addValueLabel(`${stressScore.toFixed(1)}/10`, new THREE.Vector3(6, stressScore + 1, 0)));
  }

  function createBar(x, height, z, width, depth, color) {
    // Ensure minimum height
    const barHeight = Math.max(0.1, height);

    // Create geometry and material
    const geometry = new THREE.BoxGeometry(width, barHeight, depth);
    const material = new THREE.MeshPhongMaterial({
      color: color,
      transparent: true,
      opacity: 0.8,
      emissive: color,
      emissiveIntensity: 0.2
    });

    // Create mesh
    const bar = new THREE.Mesh(geometry, material);
    bar.position.set(x, barHeight / 2, z);

    return bar;
  }

  function addAxisLabel(text, position, color) {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    canvas.width = 256;
    canvas.height = 64;

    // Clear canvas
    context.fillStyle = 'rgba(0, 0, 0, 0)';
    context.fillRect(0, 0, canvas.width, canvas.height);

    // Draw text
    context.font = 'bold 24px Montserrat, Arial';
    context.fillStyle = '#' + new THREE.Color(color).getHexString();
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    context.fillText(text, canvas.width / 2, canvas.height / 2);

    // Create sprite
    const texture = new THREE.CanvasTexture(canvas);
    const material = new THREE.SpriteMaterial({ map: texture, transparent: true });
    const sprite = new THREE.Sprite(material);
    sprite.position.copy(position);
    sprite.scale.set(5, 1.25, 1);

    scene.add(sprite);
    return sprite;
  }

  function addValueLabel(text, position) {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    canvas.width = 128;
    canvas.height = 64;

    // Clear canvas
    context.fillStyle = 'rgba(0, 0, 0, 0)';
    context.fillRect(0, 0, canvas.width, canvas.height);

    // Draw text
    context.font = 'bold 20px Montserrat, Arial';
    context.fillStyle = '#ffffff';
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    context.fillText(text, canvas.width / 2, canvas.height / 2);

    // Create sprite
    const texture = new THREE.CanvasTexture(canvas);
    const material = new THREE.SpriteMaterial({ map: texture, transparent: true });
    const sprite = new THREE.Sprite(material);
    sprite.position.copy(position);
    sprite.scale.set(2, 1, 1);

    scene.add(sprite);
    return sprite;
  }

  function animate() {
    try {
      // Check if all required objects exist
      if (!renderer || !scene || !camera || !controls) {
        console.warn("Required 3D objects not initialized yet");
        requestAnimationFrame(animate);
        return;
      }

      requestAnimationFrame(animate);
      controls.update();
      renderer.render(scene, camera);
    } catch (error) {
      console.error("Error in animation loop:", error);
      // Continue the animation loop even if there's an error
      requestAnimationFrame(animate);
    }
  }

  function onWindowResize() {
    try {
      // Check if renderer and camera exist
      if (!renderer || !camera) {
        console.warn("Renderer or camera not initialized yet");
        return;
      }

      const container = document.getElementById('threeDGraph');
      if (!container) return;

      // Get container dimensions
      // Use parent container dimensions if the container itself has no dimensions yet
      const parentContainer = container.parentElement;
      let width = container.clientWidth || parentContainer.clientWidth || 500;
      let height = container.clientHeight || parentContainer.clientHeight || 350;

      // Ensure minimum dimensions
      width = Math.max(width, 300);
      height = Math.max(height, 300);

      console.log(`Resizing graph to: ${width}x${height}`);

      // Update camera aspect ratio
      camera.aspect = width / height;
      camera.updateProjectionMatrix();

      // Resize renderer
      renderer.setSize(width, height);
      renderer.setPixelRatio(window.devicePixelRatio);

      // Force a render to update the display
      if (scene) {
        renderer.render(scene, camera);
      }
    } catch (error) {
      console.error("Error resizing graph:", error);
    }
  }

  function initMoodChart() {
    const ctx = document.getElementById('moodChart');
    if (!ctx) return;

    const moodLabels = ['Happy', 'Calm', 'Neutral', 'Anxious', 'Sad', 'Angry', 'Exhausted'];
    const moodData = [0, 0, 1, 0, 0, 0, 0]; // Start with neutral mood

    window.moodChart = new Chart(ctx, {
      type: 'radar',
      data: {
        labels: moodLabels,
        datasets: [{
          label: 'Mood Frequency',
          data: moodData,
          backgroundColor: 'rgba(129, 140, 248, 0.2)',
          borderColor: 'rgba(129, 140, 248, 1)',
          pointBackgroundColor: 'rgba(129, 140, 248, 1)',
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: 'rgba(129, 140, 248, 1)'
        }]
      },
      options: {
        scales: {
          r: {
            beginAtZero: true,
            ticks: {
              stepSize: 1
            },
            grid: {
              color: 'rgba(255, 255, 255, 0.1)'
            },
            angleLines: {
              color: 'rgba(255, 255, 255, 0.1)'
            },
            pointLabels: {
              color: 'rgba(226, 232, 240, 0.8)',
              font: {
                family: 'Poppins'
              }
            }
          }
        },
        plugins: {
          legend: {
            labels: {
              color: 'rgba(226, 232, 240, 0.8)',
              font: {
                family: 'Poppins'
              }
            }
          }
        }
      }
    });
  }

  function updateMoodChart() {
    if (!window.moodChart) return;

    // Count mood frequencies
    const moodCounts = {
      'happy': 0,
      'calm': 0,
      'neutral': 0,
      'anxious': 0,
      'sad': 0,
      'angry': 0,
      'exhausted': 0
    };

    mockHistory.forEach(entry => {
      if (moodCounts[entry.mood] !== undefined) {
        moodCounts[entry.mood]++;
      }
    });

    // Update chart data
    window.moodChart.data.datasets[0].data = [
      moodCounts.happy,
      moodCounts.calm,
      moodCounts.neutral,
      moodCounts.anxious,
      moodCounts.sad,
      moodCounts.angry,
      moodCounts.exhausted
    ];

    window.moodChart.update();
  }

  function generateStressDescription(stressScore, sleepHours, sleepQuality, mood) {
    let description = '';
    let tip = '';

    // Generate description based on stress level
    if (stressScore >= 7) {
      description = `<span class="stress-high">Your stress level is high (${stressScore.toFixed(1)}/10)</span>. `;

      if (sleepHours < 7) {
        description += `Your sleep duration of ${sleepHours} hours is below the recommended 7-9 hours, which is likely contributing to your stress. `;
      }

      if (sleepQuality < 6) {
        description += `Your sleep quality rating of ${sleepQuality}/10 indicates poor sleep, which can significantly impact stress levels. `;
      }

      if (mood === 'anxious' || mood === 'angry' || mood === 'exhausted' || mood === 'sad') {
        description += `Your current ${mood} mood is also a factor in your high stress level. `;
      }

      description += "Consider implementing stress reduction techniques and improving your sleep habits.";

      // Generate tip for high stress
      const highStressTips = [
        "Practice deep breathing exercises for 5 minutes before bed to calm your nervous system.",
        "Try progressive muscle relaxation to release physical tension before sleep.",
        "Consider limiting screen time to at least 1 hour before bedtime to improve sleep quality.",
        "Create a consistent sleep schedule, even on weekends, to regulate your body's internal clock.",
        "Engage in moderate physical activity during the day, but avoid intense exercise close to bedtime."
      ];

      tip = highStressTips[Math.floor(Math.random() * highStressTips.length)];

    } else if (stressScore >= 4) {
      description = `<span class="stress-moderate">Your stress level is moderate (${stressScore.toFixed(1)}/10)</span>. `;

      if (sleepHours < 7) {
        description += `While you're getting ${sleepHours} hours of sleep, increasing to 7-9 hours could help reduce your stress. `;
      } else {
        description += `Your sleep duration of ${sleepHours} hours is good, but other factors are affecting your stress. `;
      }

      if (sleepQuality < 7) {
        description += `Improving your sleep quality beyond ${sleepQuality}/10 could help lower stress levels. `;
      }

      if (mood === 'neutral' || mood === 'anxious' || mood === 'sad') {
        description += `Your ${mood} mood is contributing to your moderate stress level. `;
      }

      description += "With some adjustments to your routine, you can reduce your stress further.";

      // Generate tip for moderate stress
      const moderateStressTips = [
        "Try a calming bedtime routine like reading or gentle stretching to prepare for sleep.",
        "Consider using white noise or nature sounds to create a more peaceful sleep environment.",
        "Limit caffeine intake after noon to improve sleep quality.",
        "Practice mindfulness meditation for 10 minutes daily to reduce overall stress.",
        "Keep a worry journal before bed to clear your mind of stressful thoughts."
      ];

      tip = moderateStressTips[Math.floor(Math.random() * moderateStressTips.length)];

    } else {
      description = `<span class="stress-low">Your stress level is low (${stressScore.toFixed(1)}/10)</span>. `;

      if (sleepHours >= 7 && sleepHours <= 9) {
        description += `You're getting an optimal amount of sleep (${sleepHours} hours), which is helping manage stress. `;
      } else if (sleepHours > 9) {
        description += `You're getting ${sleepHours} hours of sleep, which is more than the recommended amount. While rest is important, excessive sleep can sometimes affect mood. `;
      }

      if (sleepQuality >= 7) {
        description += `Your high sleep quality (${sleepQuality}/10) is contributing positively to your low stress levels. `;
      }

      if (mood === 'happy' || mood === 'calm') {
        description += `Your ${mood} mood reflects and reinforces your low stress state. `;
      }

      description += "Keep up your good habits to maintain these low stress levels.";

      // Generate tip for low stress
      const lowStressTips = [
        "Continue your good sleep habits and consider sharing your routine with others who struggle with stress.",
        "Maintain your current sleep schedule while experimenting with small optimizations for even better rest.",
        "Consider keeping a sleep journal to identify what factors contribute to your good sleep quality.",
        "Your low stress levels are ideal for creative pursuits or learning new skills.",
        "Use your current well-rested state to build resilience for future stressful periods."
      ];

      tip = lowStressTips[Math.floor(Math.random() * lowStressTips.length)];
    }

    // Update the DOM
    stressDescription.innerHTML = description;
    healthTip.textContent = tip;
  }

  function updateSleepPatterns(sleepHours, sleepQuality, mood) {
    if (!sleepPatterns) return;

    // Clear previous content
    sleepPatterns.innerHTML = '';

    // Create sleep pattern analysis
    const patternContainer = document.createElement('div');
    patternContainer.className = 'sleep-pattern-analysis';

    // Sleep duration analysis
    const durationAnalysis = document.createElement('div');
    durationAnalysis.className = 'pattern-item';

    const durationIcon = document.createElement('div');
    durationIcon.className = 'pattern-icon';
    durationIcon.innerHTML = '<i class="fas fa-clock"></i>';

    const durationContent = document.createElement('div');
    durationContent.className = 'pattern-content';

    const durationTitle = document.createElement('h4');
    durationTitle.className = 'pattern-title';
    durationTitle.textContent = 'Sleep Duration';

    const durationText = document.createElement('p');
    if (sleepHours >= 7 && sleepHours <= 9) {
      durationText.innerHTML = `<span class="optimal">Optimal</span>: Your sleep duration of ${sleepHours} hours falls within the recommended 7-9 hours for adults.`;
    } else if (sleepHours < 7) {
      durationText.innerHTML = `<span class="suboptimal">Below Recommended</span>: Your sleep duration of ${sleepHours} hours is less than the recommended 7-9 hours for adults.`;
    } else {
      durationText.innerHTML = `<span class="attention">Above Average</span>: Your sleep duration of ${sleepHours} hours exceeds the typical recommendation. While extra sleep can be beneficial occasionally, consistently sleeping more than 9 hours may be worth discussing with a healthcare provider.`;
    }

    durationContent.appendChild(durationTitle);
    durationContent.appendChild(durationText);
    durationAnalysis.appendChild(durationIcon);
    durationAnalysis.appendChild(durationContent);

    // Sleep quality analysis
    const qualityAnalysis = document.createElement('div');
    qualityAnalysis.className = 'pattern-item';

    const qualityIcon = document.createElement('div');
    qualityIcon.className = 'pattern-icon';
    qualityIcon.innerHTML = '<i class="fas fa-star"></i>';

    const qualityContent = document.createElement('div');
    qualityContent.className = 'pattern-content';

    const qualityTitle = document.createElement('h4');
    qualityTitle.className = 'pattern-title';
    qualityTitle.textContent = 'Sleep Quality';

    const qualityText = document.createElement('p');
    if (sleepQuality >= 7) {
      qualityText.innerHTML = `<span class="optimal">Good</span>: Your sleep quality rating of ${sleepQuality}/10 indicates you're experiencing restful sleep.`;
    } else if (sleepQuality >= 4) {
      qualityText.innerHTML = `<span class="attention">Moderate</span>: Your sleep quality rating of ${sleepQuality}/10 suggests room for improvement in your sleep experience.`;
    } else {
      qualityText.innerHTML = `<span class="suboptimal">Poor</span>: Your sleep quality rating of ${sleepQuality}/10 indicates you're not getting restful sleep, which can significantly impact your well-being.`;
    }

    qualityContent.appendChild(qualityTitle);
    qualityContent.appendChild(qualityText);
    qualityAnalysis.appendChild(qualityIcon);
    qualityAnalysis.appendChild(qualityContent);

    // Mood impact analysis
    const moodAnalysis = document.createElement('div');
    moodAnalysis.className = 'pattern-item';

    const moodIcon = document.createElement('div');
    moodIcon.className = 'pattern-icon';

    // Set icon based on mood
    let moodIconClass = 'fa-meh';
    if (mood === 'happy' || mood === 'calm') {
      moodIconClass = 'fa-smile';
    } else if (mood === 'anxious' || mood === 'sad' || mood === 'angry' || mood === 'exhausted') {
      moodIconClass = 'fa-frown';
    }
    moodIcon.innerHTML = `<i class="fas ${moodIconClass}"></i>`;

    const moodContent = document.createElement('div');
    moodContent.className = 'pattern-content';

    const moodTitle = document.createElement('h4');
    moodTitle.className = 'pattern-title';
    moodTitle.textContent = 'Mood Impact';

    const moodText = document.createElement('p');
    if (mood === 'happy' || mood === 'calm') {
      moodText.innerHTML = `Your <span class="optimal">${mood}</span> mood suggests a positive emotional state, which both contributes to and results from good sleep.`;
    } else if (mood === 'neutral') {
      moodText.innerHTML = `Your <span class="attention">neutral</span> mood neither significantly helps nor hinders your sleep quality.`;
    } else {
      moodText.innerHTML = `Your <span class="suboptimal">${mood}</span> mood may be both affecting and affected by your sleep patterns. Addressing emotional well-being can create a positive cycle with sleep.`;
    }

    moodContent.appendChild(moodTitle);
    moodContent.appendChild(moodText);
    moodAnalysis.appendChild(moodIcon);
    moodAnalysis.appendChild(moodContent);

    // Add all analyses to the container
    patternContainer.appendChild(durationAnalysis);
    patternContainer.appendChild(qualityAnalysis);
    patternContainer.appendChild(moodAnalysis);

    // Add container to sleep patterns section
    sleepPatterns.appendChild(patternContainer);

    // Add CSS for the new elements
    const style = document.createElement('style');
    style.textContent = `
      .sleep-pattern-analysis {
        display: flex;
        flex-direction: column;
        gap: 1rem;
      }

      .pattern-item {
        display: flex;
        gap: 1rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 10px;
        padding: 1rem;
        border: 1px solid var(--glass-border);
      }

      .pattern-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: rgba(56, 189, 248, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--accent-blue);
        font-size: 1.2rem;
        flex-shrink: 0;
      }

      .pattern-content {
        flex: 1;
      }

      .pattern-title {
        font-family: 'Montserrat', sans-serif;
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--text-light);
      }

      .pattern-content p {
        color: var(--text-muted);
        line-height: 1.5;
      }

      .optimal {
        color: var(--success-green);
        font-weight: 600;
      }

      .attention {
        color: var(--warning-yellow);
        font-weight: 600;
      }

      .suboptimal {
        color: var(--danger-red);
        font-weight: 600;
      }

      .stress-high {
        color: var(--danger-red);
        font-weight: 600;
      }

      .stress-moderate {
        color: var(--warning-yellow);
        font-weight: 600;
      }

      .stress-low {
        color: var(--success-green);
        font-weight: 600;
      }
    `;
    document.head.appendChild(style);
  }

  function shakeElement(element) {
    element.classList.add('shake');
    setTimeout(() => {
      element.classList.remove('shake');
    }, 500);

    // Add shake animation CSS if not already added
    if (!document.querySelector('#shake-animation')) {
      const style = document.createElement('style');
      style.id = 'shake-animation';
      style.textContent = `
        @keyframes shake {
          0%, 100% { transform: translateX(0); }
          10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
          20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        .shake {
          animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
          border-color: var(--danger-red) !important;
        }
      `;
      document.head.appendChild(style);
    }
  }

  function showToast(message) {
    // Remove existing toast if any
    const existingToast = document.querySelector('.toast-notification');
    if (existingToast) {
      existingToast.remove();
    }

    // Create toast element
    const toast = document.createElement('div');
    toast.className = 'toast-notification';
    toast.textContent = message;

    // Add toast to body
    document.body.appendChild(toast);

    // Add CSS for toast if not already added
    if (!document.querySelector('#toast-style')) {
      const style = document.createElement('style');
      style.id = 'toast-style';
      style.textContent = `
        .toast-notification {
          position: fixed;
          bottom: 20px;
          left: 50%;
          transform: translateX(-50%);
          background: var(--glass-bg);
          color: var(--text-light);
          padding: 12px 24px;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
          z-index: 1000;
          font-size: 0.9rem;
          backdrop-filter: blur(10px);
          border: 1px solid var(--glass-border);
          animation: toastIn 0.3s ease-out forwards, toastOut 0.3s ease-in forwards 3s;
        }

        @keyframes toastIn {
          from { opacity: 0; transform: translate(-50%, 20px); }
          to { opacity: 1; transform: translate(-50%, 0); }
        }

        @keyframes toastOut {
          from { opacity: 1; transform: translate(-50%, 0); }
          to { opacity: 0; transform: translate(-50%, 20px); }
        }
      `;
      document.head.appendChild(style);
    }

    // Remove toast after animation
    setTimeout(() => {
      toast.remove();
    }, 3500);
  }

  // Export PDF function
  function exportPDF() {
    try {
      // Create new document
      const doc = new jsPDF();

      // Add title
      doc.setFontSize(20);
      doc.text("Stress Analysis Report", 105, 20, { align: "center" });

      // Add user info
      doc.setFontSize(12);
      doc.text(`User: ${username}`, 20, 40);
      doc.text(`Date: ${new Date().toLocaleDateString()}`, 20, 50);

      // Add sleep data
      doc.setFontSize(16);
      doc.text("Sleep Data", 20, 70);
      doc.setFontSize(12);
      doc.text(`Sleep Hours: ${sleepHoursInput.value}`, 30, 80);
      doc.text(`Sleep Quality: ${sleepQualityInput.value}/10`, 30, 90);
      doc.text(`Current Mood: ${currentMoodSelect.value}`, 30, 100);

      // Add stress analysis
      doc.setFontSize(16);
      doc.text("Stress Analysis", 20, 120);
      doc.setFontSize(12);

      // Get stress level text
      const stressScore = parseFloat(stressLevel.textContent);
      let stressLevelText;
      if (stressScore >= 7) {
        stressLevelText = "High Stress";
      } else if (stressScore >= 4) {
        stressLevelText = "Moderate Stress";
      } else {
        stressLevelText = "Low Stress";
      }

      doc.text(`Stress Level: ${stressScore.toFixed(1)}/10 (${stressLevelText})`, 30, 130);

      // Add health tip
      doc.setFontSize(16);
      doc.text("Health Tip", 20, 150);
      doc.setFontSize(12);

      // Split long text into multiple lines
      const tipText = healthTip.textContent;
      const splitTip = doc.splitTextToSize(tipText, 170);
      doc.text(splitTip, 30, 160);

      // Save PDF
      doc.save(`stress_analysis_${new Date().toLocaleDateString().replace(/\//g, '-')}.pdf`);

      showToast("PDF exported successfully!");
    } catch (error) {
      console.error("PDF export error:", error);
      showToast("Failed to export PDF. Please try again.");
    }
  }

  // Make exportPDF function globally accessible
  window.exportPDF = exportPDF;

  // Create a more comprehensive PDF report with all user data
  function exportFullReport() {
    try {
      console.log("Starting PDF export process...");

      // Check if jsPDF is available
      if (typeof jsPDF === 'undefined') {
        console.error("jsPDF is undefined");

        // Check for alternative ways jsPDF might be available
        if (typeof window.jsPDF !== 'undefined') {
          console.log("Found jsPDF on window object");
          window.jsPDF = window.jsPDF;
        } else if (typeof window.jspdf !== 'undefined' && typeof window.jspdf.jsPDF !== 'undefined') {
          console.log("Found jsPDF on window.jspdf object");
          window.jsPDF = window.jspdf.jsPDF;
        } else {
          showToast("PDF library not loaded. Please refresh the page.");
          return;
        }
      }

      console.log("jsPDF availability check passed");

      // Create new document (landscape orientation for more space)
      let doc;
      try {
        // Try different ways to create a jsPDF instance
        if (typeof jsPDF === 'function') {
          console.log("Creating jsPDF instance using constructor function");
          doc = new jsPDF({
            orientation: 'landscape',
            unit: 'mm',
            format: 'a4'
          });
        } else if (typeof window.jsPDF === 'function') {
          console.log("Creating jsPDF instance using window.jsPDF");
          doc = new window.jsPDF({
            orientation: 'landscape',
            unit: 'mm',
            format: 'a4'
          });
        } else if (typeof window.jspdf !== 'undefined' && typeof window.jspdf.jsPDF === 'function') {
          console.log("Creating jsPDF instance using window.jspdf.jsPDF");
          doc = new window.jspdf.jsPDF({
            orientation: 'landscape',
            unit: 'mm',
            format: 'a4'
          });
        } else {
          throw new Error("Could not find a valid jsPDF constructor");
        }
      } catch (e) {
        console.error("Error creating jsPDF instance:", e);
        showToast("Error creating PDF. Please try again.");
        return;
      }

      console.log("jsPDF instance created successfully");

      // Add header with logo and title
      doc.setFontSize(24);
      doc.setTextColor(56, 189, 248); // Blue color
      doc.text("SleepSense AI", 149, 20, { align: "center" });

      // Add a simple brain logo
      try {
        // Draw a simple brain icon
        doc.setDrawColor(56, 189, 248); // Blue color
        doc.setLineWidth(0.5);

        // Brain outline
        doc.ellipse(149, 10, 5, 4, 'F');
        doc.setFillColor(255, 255, 255);
        doc.ellipse(149, 10, 4.5, 3.5, 'F');

        // Brain details
        doc.setDrawColor(129, 140, 248); // Purple color
        doc.setLineWidth(0.3);
        doc.line(146, 8, 148, 12);
        doc.line(148, 12, 150, 8);
        doc.line(150, 8, 152, 12);
        doc.line(145, 10, 153, 10);
      } catch (e) {
        console.log("Error drawing brain icon, skipping:", e);
      }

      doc.setFontSize(18);
      doc.setTextColor(129, 140, 248); // Purple color
      doc.text("Comprehensive Stress Analysis Report", 149, 30, { align: "center" });

      // Add horizontal line
      doc.setDrawColor(129, 140, 248); // Purple color
      doc.setLineWidth(0.5);
      doc.line(20, 35, 277, 35);

      // Add user info section
      doc.setFontSize(14);
      doc.setTextColor(0, 0, 0); // Black color
      doc.text("User Information", 20, 45);

      doc.setFontSize(12);
      doc.text(`User: ${username}`, 25, 55);
      doc.text(`Report Generated: ${new Date().toLocaleString()}`, 25, 62);

      // Create two-column layout
      const leftColumnX = 20;
      const rightColumnX = 150;
      const columnWidth = 120;
      let leftColumnY = 80;
      let rightColumnY = 80;

      // Add current sleep data section (left column)
      doc.setFontSize(14);
      doc.setTextColor(0, 0, 0);
      doc.text("Current Sleep Data", leftColumnX, leftColumnY);
      leftColumnY += 10;

      doc.setFontSize(12);
      const sleepHours = sleepHoursInput && sleepHoursInput.value ? sleepHoursInput.value : "Not entered";
      const sleepQuality = sleepQualityInput && sleepQualityInput.value ? sleepQualityInput.value + "/10" : "Not entered";
      const currentMood = currentMoodSelect ? currentMoodSelect.value : "Not selected";

      doc.text(`Sleep Hours: ${sleepHours}`, leftColumnX + 5, leftColumnY);
      leftColumnY += 7;
      doc.text(`Sleep Quality: ${sleepQuality}`, leftColumnX + 5, leftColumnY);
      leftColumnY += 7;
      doc.text(`Current Mood: ${currentMood}`, leftColumnX + 5, leftColumnY);
      leftColumnY += 15;

      // Add historical data section (right column)
      doc.setFontSize(14);
      doc.setTextColor(0, 0, 0);
      doc.text("Historical Data", rightColumnX, rightColumnY);
      rightColumnY += 10;

      // Add average stats
      doc.setFontSize(12);
      const avgSleepHoursText = avgSleepHours ? avgSleepHours.textContent : "N/A";
      const avgSleepQualityText = avgSleepQuality ? avgSleepQuality.textContent : "N/A";
      const avgStressLevelText = avgStressLevel ? avgStressLevel.textContent : "N/A";

      doc.text(`Average Sleep Hours: ${avgSleepHoursText}`, rightColumnX + 5, rightColumnY);
      rightColumnY += 7;
      doc.text(`Average Sleep Quality: ${avgSleepQualityText}`, rightColumnX + 5, rightColumnY);
      rightColumnY += 7;
      doc.text(`Average Stress Level: ${avgStressLevelText}`, rightColumnX + 5, rightColumnY);
      rightColumnY += 15;

      // Add stress analysis section (left column)
      doc.setFontSize(14);
      doc.setTextColor(0, 0, 0);
      doc.text("Stress Analysis", leftColumnX, leftColumnY);
      leftColumnY += 10;

      doc.setFontSize(12);
      const stressScore = stressLevel ? parseFloat(stressLevel.textContent) : 0;
      let stressLevelText = "Not calculated";
      let stressColor = [0, 0, 0]; // Default black

      if (!isNaN(stressScore)) {
        if (stressScore >= 7) {
          stressLevelText = "High Stress";
          stressColor = [239, 68, 68]; // Red
        } else if (stressScore >= 4) {
          stressLevelText = "Moderate Stress";
          stressColor = [245, 158, 11]; // Yellow
        } else {
          stressLevelText = "Low Stress";
          stressColor = [16, 185, 129]; // Green
        }
      }

      doc.setTextColor(stressColor[0], stressColor[1], stressColor[2]);
      doc.text(`Current Stress Level: ${stressScore.toFixed(1)}/10 (${stressLevelText})`, leftColumnX + 5, leftColumnY);
      leftColumnY += 7;

      // Reset text color
      doc.setTextColor(0, 0, 0);

      // Add stress description
      if (stressDescription && stressDescription.textContent) {
        const descriptionText = stressDescription.textContent.replace(/<\/?[^>]+(>|$)/g, ""); // Remove HTML tags
        const splitDescription = doc.splitTextToSize(descriptionText, columnWidth);
        doc.text(splitDescription, leftColumnX + 5, leftColumnY);
        leftColumnY += splitDescription.length * 7 + 5;
      } else {
        doc.text("No stress description available.", leftColumnX + 5, leftColumnY);
        leftColumnY += 12;
      }

      // Add health tip (left column)
      doc.setFontSize(14);
      doc.setDrawColor(56, 189, 248); // Blue color
      doc.setLineWidth(0.2);
      doc.line(leftColumnX, leftColumnY - 5, leftColumnX + columnWidth, leftColumnY - 5);
      doc.text("Health Tip", leftColumnX, leftColumnY);
      leftColumnY += 10;

      doc.setFillColor(240, 249, 255); // Light blue background
      doc.rect(leftColumnX, leftColumnY - 5, columnWidth, 20, 'F');

      doc.setFontSize(12);
      if (healthTip && healthTip.textContent) {
        const tipText = healthTip.textContent;
        const splitTip = doc.splitTextToSize(tipText, columnWidth - 10);
        doc.text(splitTip, leftColumnX + 5, leftColumnY);
      } else {
        doc.text("No health tip available.", leftColumnX + 5, leftColumnY);
      }

      // Add history table (right column)
      doc.setFontSize(14);
      doc.text("Recent Sleep History", rightColumnX, rightColumnY);
      rightColumnY += 10;

      // Draw table
      const tableX = rightColumnX;
      const tableWidth = 120;
      const rowHeight = 8;
      const colWidths = [25, 25, 25, 25];

      // Draw table header background
      doc.setFillColor(230, 236, 245); // Light blue-gray
      doc.rect(tableX, rightColumnY - 5, tableWidth, rowHeight, 'F');

      // Table headers
      doc.setFontSize(10);
      doc.setTextColor(50, 50, 50);
      doc.text("Date", tableX + 3, rightColumnY);
      doc.text("Sleep Hours", tableX + colWidths[0] + 3, rightColumnY);
      doc.text("Sleep Quality", tableX + colWidths[0] + colWidths[1] + 3, rightColumnY);
      doc.text("Mood", tableX + colWidths[0] + colWidths[1] + colWidths[2] + 3, rightColumnY);
      rightColumnY += rowHeight;

      // Draw table grid lines
      doc.setDrawColor(200, 200, 200);
      doc.setLineWidth(0.1);

      // Horizontal header line
      doc.line(tableX, rightColumnY - rowHeight, tableX + tableWidth, rightColumnY - rowHeight);

      // Store the starting Y position for vertical lines
      const tableStartY = rightColumnY - rowHeight;
      // We'll draw vertical lines after adding all rows

      // Table data
      doc.setTextColor(0, 0, 0);
      doc.setFontSize(10);

      // Use mockHistory data or create sample data if not available
      const historyData = mockHistory || [
        { date: "2023-07-15", sleepHours: 7.5, sleepQuality: 8, mood: 'happy', stressLevel: 3.2 },
        { date: "2023-07-14", sleepHours: 6, sleepQuality: 5, mood: 'neutral', stressLevel: 5.8 },
        { date: "2023-07-13", sleepHours: 8, sleepQuality: 7, mood: 'calm', stressLevel: 2.5 },
        { date: "2023-07-12", sleepHours: 5.5, sleepQuality: 4, mood: 'anxious', stressLevel: 6.7 },
        { date: "2023-07-11", sleepHours: 7, sleepQuality: 6, mood: 'neutral', stressLevel: 4.5 }
      ];

      // Add current date to history entries if not present
      historyData.forEach((entry, index) => {
        if (!entry.date) {
          // Create a date by subtracting days from current date
          const date = new Date();
          date.setDate(date.getDate() - index);
          entry.date = date.toLocaleDateString();
        }

        // Alternate row background colors
        if (index % 2 === 0) {
          doc.setFillColor(248, 250, 252); // Very light gray
          doc.rect(tableX, rightColumnY - 5, tableWidth, rowHeight, 'F');
        }

        // Add table row data with proper vertical alignment
        doc.text(entry.date.toString(), tableX + 3, rightColumnY);
        doc.text(entry.sleepHours.toString(), tableX + colWidths[0] + 3, rightColumnY);
        doc.text(entry.sleepQuality.toString(), tableX + colWidths[0] + colWidths[1] + 3, rightColumnY);
        doc.text(entry.mood, tableX + colWidths[0] + colWidths[1] + colWidths[2] + 3, rightColumnY);

        // Draw horizontal line after each row
        doc.setDrawColor(200, 200, 200);
        doc.line(tableX, rightColumnY + 3, tableX + tableWidth, rightColumnY + 3);

        rightColumnY += rowHeight;

        // Only show up to 5 entries to avoid overflow
        if (index >= 4) return;
      });

      // Now draw the vertical lines for the table after we know the final height
      const tableEndY = rightColumnY;
      doc.setDrawColor(200, 200, 200);
      doc.setLineWidth(0.1);

      // Draw vertical lines from top to bottom of table
      doc.line(tableX, tableStartY, tableX, tableEndY);
      doc.line(tableX + colWidths[0], tableStartY, tableX + colWidths[0], tableEndY);
      doc.line(tableX + colWidths[0] + colWidths[1], tableStartY, tableX + colWidths[0] + colWidths[1], tableEndY);
      doc.line(tableX + colWidths[0] + colWidths[1] + colWidths[2], tableStartY, tableX + colWidths[0] + colWidths[1] + colWidths[2], tableEndY);
      doc.line(tableX + tableWidth, tableStartY, tableX + tableWidth, tableEndY);

      // Add a decorative element at the bottom
      doc.setDrawColor(56, 189, 248); // Blue color
      doc.setLineWidth(0.5);
      doc.line(20, 180, 277, 180);

      // Add footer
      doc.setFontSize(10);
      doc.setTextColor(100, 100, 100);
      doc.text("Generated by SleepSense AI - Your Personal Stress Management Assistant", 149, 190, { align: "center" });

      // Add timestamp
      doc.setFontSize(8);
      doc.text(`Report ID: SS-${new Date().getTime().toString().substring(5)}`, 20, 195);
      doc.text(`Generated on: ${new Date().toLocaleString()}`, 277, 195, { align: "right" });

      // Save PDF with a descriptive filename
      try {
        console.log("Preparing to save PDF...");
        const dateStr = new Date().toLocaleDateString().replace(/\//g, '-');
        const filename = `SleepSense_Full_Report_${username || 'User'}_${dateStr}.pdf`;
        console.log("Saving PDF with filename:", filename);

        doc.save(filename);
        console.log("PDF saved successfully");

        showToast("Full report exported successfully!");
      } catch (saveError) {
        console.error("Error saving PDF:", saveError);

        // Try alternative approach
        try {
          console.log("Trying alternative PDF save approach...");
          const blob = doc.output('blob');
          const url = URL.createObjectURL(blob);

          const link = document.createElement('a');
          link.href = url;
          link.download = `SleepSense_Report_${new Date().getTime()}.pdf`;
          link.style.display = 'none';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          console.log("Alternative PDF save approach successful");
          showToast("Full report exported successfully!");
        } catch (altError) {
          console.error("Alternative PDF save approach failed:", altError);
          showToast("Failed to export PDF. Please try again.");
        }
      }
    } catch (error) {
      console.error("PDF export error:", error);
      showToast("Failed to export PDF. Error: " + error.message);
    }
  }

  // Add event listener for the full report button
  const exportFullReportBtn = document.getElementById('exportFullReportBtn');
  if (exportFullReportBtn) {
    exportFullReportBtn.addEventListener('click', function() {
      // Check if jsPDF is available, if not, try to load it dynamically
      if (typeof jsPDF === 'undefined' && typeof window.jsPDF === 'undefined' &&
          (typeof window.jspdf === 'undefined' || typeof window.jspdf.jsPDF === 'undefined')) {

        console.log("jsPDF not found, attempting to load it dynamically");

        // Create a script element to load jsPDF
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
        script.onload = function() {
          console.log("jsPDF loaded dynamically");

          // Initialize jsPDF
          if (typeof window.jspdf === 'undefined') {
            window.jspdf = {};
          }

          if (typeof window.jspdf.jsPDF === 'undefined' && typeof window.jsPDF !== 'undefined') {
            window.jspdf.jsPDF = window.jsPDF;
          }

          // Now try to export the PDF
          setTimeout(exportFullReport, 500);
        };

        script.onerror = function() {
          console.error("Failed to load jsPDF dynamically");
          showToast("Failed to load PDF library. Please check your internet connection and try again.");
        };

        document.head.appendChild(script);
      } else {
        // jsPDF is available, proceed with export
        exportFullReport();
      }
    });
  }

  // Make exportFullReport function globally accessible
  window.exportFullReport = exportFullReport;

  // Simple fallback PDF export function that uses a more basic approach
  function exportSimplePDF() {
    try {
      console.log("Using simple PDF export fallback...");

      // Try to get jsPDF constructor
      let jsPDFConstructor;
      if (typeof jsPDF === 'function') {
        jsPDFConstructor = jsPDF;
      } else if (typeof window.jsPDF === 'function') {
        jsPDFConstructor = window.jsPDF;
      } else if (typeof window.jspdf !== 'undefined' && typeof window.jspdf.jsPDF === 'function') {
        jsPDFConstructor = window.jspdf.jsPDF;
      } else {
        showToast("PDF library not available. Please refresh the page.");
        return;
      }

      // Create a simple PDF
      const doc = new jsPDFConstructor();

      // Add title
      doc.setFontSize(22);
      doc.text("SleepSense AI - Stress Report", 105, 20, { align: "center" });

      // Add user info
      doc.setFontSize(14);
      doc.text(`User: ${username || 'User'}`, 20, 40);
      doc.text(`Date: ${new Date().toLocaleDateString()}`, 20, 50);

      // Add sleep data if available
      doc.setFontSize(16);
      doc.text("Sleep Data", 20, 70);

      doc.setFontSize(12);
      if (sleepHoursInput && sleepQualityInput && currentMoodSelect) {
        doc.text(`Sleep Hours: ${sleepHoursInput.value || 'Not entered'}`, 30, 80);
        doc.text(`Sleep Quality: ${sleepQualityInput.value || 'Not entered'}/10`, 30, 90);
        doc.text(`Current Mood: ${currentMoodSelect.value || 'Not selected'}`, 30, 100);
      } else {
        doc.text("Sleep data not available", 30, 80);
      }

      // Add stress level if available
      doc.setFontSize(16);
      doc.text("Stress Analysis", 20, 120);

      doc.setFontSize(12);
      if (stressLevel) {
        doc.text(`Stress Level: ${stressLevel.textContent}/10`, 30, 130);
      } else {
        doc.text("Stress level not calculated", 30, 130);
      }

      // Add health tip if available
      if (healthTip) {
        doc.setFontSize(16);
        doc.text("Health Tip", 20, 150);

        doc.setFontSize(12);
        const tipText = healthTip.textContent || "No health tip available";
        const splitTip = doc.splitTextToSize(tipText, 170);
        doc.text(splitTip, 30, 160);
      }

      // Save the PDF
      try {
        doc.save(`SleepSense_Report_${new Date().getTime()}.pdf`);
        showToast("Basic report exported successfully!");
      } catch (e) {
        console.error("Error saving simple PDF:", e);

        // Try alternative approach with data URL
        try {
          const pdfData = doc.output('datauristring');
          const newWindow = window.open();
          newWindow.document.open();
          newWindow.document.body.innerHTML = "<iframe width='100%' height='100%' src='" + pdfData + "'></iframe>";
          newWindow.document.close();
          showToast("Report opened in new tab!");
        } catch (e2) {
          console.error("Alternative PDF display failed:", e2);
          showToast("Failed to generate PDF. Please try again later.");
        }
      }
    } catch (error) {
      console.error("Simple PDF export error:", error);
      showToast("Failed to generate PDF: " + error.message);
    }
  }

  // Add fallback to export button
  if (exportFullReportBtn) {
    exportFullReportBtn.addEventListener('dblclick', function(e) {
      e.preventDefault();
      console.log("Double-click detected, using simple PDF export");
      exportSimplePDF();
    });

    // Add right-click context menu for alternative export options
    exportFullReportBtn.addEventListener('contextmenu', function(e) {
      e.preventDefault();

      // Create a context menu
      const menu = document.createElement('div');
      menu.className = 'pdf-context-menu';
      menu.innerHTML = `
        <div class="menu-item" id="html2pdf-option">Export using HTML2PDF</div>
        <div class="menu-item" id="simple-pdf-option">Export Simple PDF</div>
        <div class="menu-item" id="new-tab-option">Open in New Tab</div>
      `;

      // Style the menu
      const menuStyle = document.createElement('style');
      menuStyle.textContent = `
        .pdf-context-menu {
          position: fixed;
          background: white;
          border: 1px solid #ccc;
          border-radius: 4px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.2);
          padding: 5px 0;
          z-index: 1000;
        }
        .menu-item {
          padding: 8px 15px;
          cursor: pointer;
          transition: background 0.2s;
        }
        .menu-item:hover {
          background: #f0f0f0;
        }
      `;
      document.head.appendChild(menuStyle);

      // Position the menu
      menu.style.left = e.pageX + 'px';
      menu.style.top = e.pageY + 'px';
      document.body.appendChild(menu);

      // Add event listeners to menu items
      document.getElementById('html2pdf-option').addEventListener('click', function() {
        exportWithHtml2PDF();
        removeMenu();
      });

      document.getElementById('simple-pdf-option').addEventListener('click', function() {
        exportSimplePDF();
        removeMenu();
      });

      document.getElementById('new-tab-option').addEventListener('click', function() {
        exportToNewTab();
        removeMenu();
      });

      // Remove menu when clicking outside
      function removeMenu() {
        document.body.removeChild(menu);
        document.head.removeChild(menuStyle);
        document.removeEventListener('click', documentClickHandler);
      }

      function documentClickHandler(event) {
        if (!menu.contains(event.target)) {
          removeMenu();
        }
      }

      // Add event listener to remove menu when clicking outside
      setTimeout(() => {
        document.addEventListener('click', documentClickHandler);
      }, 0);
    });
  }

  // Export using html2pdf library
  function exportWithHtml2PDF() {
    try {
      console.log("Exporting with html2pdf...");

      if (typeof html2pdf === 'undefined') {
        console.error("html2pdf library not found");
        showToast("HTML2PDF library not available. Please refresh the page.");
        return;
      }

      // Create a container for the report content
      const reportContainer = document.createElement('div');
      reportContainer.className = 'pdf-report-container';
      reportContainer.style.padding = '20px';
      reportContainer.style.fontFamily = 'Arial, sans-serif';

      // Add report header
      const header = document.createElement('div');
      header.innerHTML = `
        <h1 style="color: #38bdf8; text-align: center; margin-bottom: 5px;">SleepSense AI</h1>
        <h2 style="color: #818cf8; text-align: center; margin-top: 0;">Stress Analysis Report</h2>
        <hr style="border: 1px solid #e2e8f0; margin: 20px 0;">
      `;
      reportContainer.appendChild(header);

      // Add user info
      const userInfo = document.createElement('div');
      userInfo.innerHTML = `
        <h3 style="color: #333;">User Information</h3>
        <p><strong>User:</strong> ${username || 'User'}</p>
        <p><strong>Report Generated:</strong> ${new Date().toLocaleString()}</p>
      `;
      reportContainer.appendChild(userInfo);

      // Add sleep data
      const sleepData = document.createElement('div');
      sleepData.innerHTML = `
        <h3 style="color: #333; margin-top: 20px;">Sleep Data</h3>
        <p><strong>Sleep Hours:</strong> ${sleepHoursInput && sleepHoursInput.value ? sleepHoursInput.value : 'Not entered'}</p>
        <p><strong>Sleep Quality:</strong> ${sleepQualityInput && sleepQualityInput.value ? sleepQualityInput.value + '/10' : 'Not entered'}</p>
        <p><strong>Current Mood:</strong> ${currentMoodSelect ? currentMoodSelect.value : 'Not selected'}</p>
      `;
      reportContainer.appendChild(sleepData);

      // Add stress analysis
      const stressAnalysis = document.createElement('div');
      const stressScore = stressLevel ? parseFloat(stressLevel.textContent) : 0;
      let stressLevelText = "Not calculated";
      let stressColor = "#000000";

      if (!isNaN(stressScore)) {
        if (stressScore >= 7) {
          stressLevelText = "High Stress";
          stressColor = "#ef4444";
        } else if (stressScore >= 4) {
          stressLevelText = "Moderate Stress";
          stressColor = "#f59e0b";
        } else {
          stressLevelText = "Low Stress";
          stressColor = "#10b981";
        }
      }

      stressAnalysis.innerHTML = `
        <h3 style="color: #333; margin-top: 20px;">Stress Analysis</h3>
        <p><strong>Current Stress Level:</strong> <span style="color: ${stressColor}; font-weight: bold;">${stressScore.toFixed(1)}/10 (${stressLevelText})</span></p>
        <p>${stressDescription ? stressDescription.textContent : 'No stress description available.'}</p>
      `;
      reportContainer.appendChild(stressAnalysis);

      // Add health tip
      const healthTipSection = document.createElement('div');
      healthTipSection.innerHTML = `
        <h3 style="color: #333; margin-top: 20px;">Health Tip</h3>
        <div style="background-color: #f8fafc; padding: 15px; border-radius: 8px; border-left: 4px solid #38bdf8;">
          <p><i style="color: #38bdf8; margin-right: 8px;">💡</i> ${healthTip ? healthTip.textContent : 'No health tip available.'}</p>
        </div>
      `;
      reportContainer.appendChild(healthTipSection);

      // Add historical data
      const historicalData = document.createElement('div');
      historicalData.innerHTML = `
        <h3 style="color: #333; margin-top: 20px;">Historical Data</h3>
        <p><strong>Average Sleep Hours:</strong> ${avgSleepHours ? avgSleepHours.textContent : 'N/A'}</p>
        <p><strong>Average Sleep Quality:</strong> ${avgSleepQuality ? avgSleepQuality.textContent : 'N/A'}</p>
        <p><strong>Average Stress Level:</strong> ${avgStressLevel ? avgStressLevel.textContent : 'N/A'}</p>
      `;
      reportContainer.appendChild(historicalData);

      // Add footer
      const footer = document.createElement('div');
      footer.innerHTML = `
        <hr style="border: 1px solid #e2e8f0; margin: 20px 0;">
        <p style="text-align: center; color: #64748b; font-size: 12px;">Generated by SleepSense AI - Your Personal Stress Management Assistant</p>
      `;
      reportContainer.appendChild(footer);

      // Temporarily add the container to the document (hidden)
      reportContainer.style.position = 'absolute';
      reportContainer.style.left = '-9999px';
      document.body.appendChild(reportContainer);

      // Generate PDF
      const options = {
        margin: 10,
        filename: `SleepSense_Report_${username || 'User'}_${new Date().toLocaleDateString().replace(/\//g, '-')}.pdf`,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { scale: 2, useCORS: true },
        jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
      };

      html2pdf().from(reportContainer).set(options).save().then(() => {
        // Remove the temporary container
        document.body.removeChild(reportContainer);
        showToast("Report exported successfully!");
      }).catch(error => {
        console.error("HTML2PDF export error:", error);
        document.body.removeChild(reportContainer);
        showToast("Failed to export PDF. Please try again.");
      });

    } catch (error) {
      console.error("HTML2PDF export error:", error);
      showToast("Failed to export PDF: " + error.message);
    }
  }

  // Export to new tab
  function exportToNewTab() {
    try {
      console.log("Exporting to new tab...");

      // Create a new window/tab
      const newWindow = window.open('', '_blank');
      if (!newWindow) {
        showToast("Pop-up blocked. Please allow pop-ups for this site.");
        return;
      }

      // Create the HTML content
      const htmlContent = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>SleepSense AI Report</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
            }
            .header h1 {
              color: #38bdf8;
              margin-bottom: 5px;
            }
            .header h2 {
              color: #818cf8;
              margin-top: 0;
            }
            .section {
              margin-bottom: 30px;
            }
            .section h3 {
              border-bottom: 1px solid #e2e8f0;
              padding-bottom: 5px;
            }
            .health-tip {
              background-color: #f8fafc;
              padding: 15px;
              border-radius: 8px;
              border-left: 4px solid #38bdf8;
            }
            .stress-level {
              font-weight: bold;
            }
            .high-stress {
              color: #ef4444;
            }
            .moderate-stress {
              color: #f59e0b;
            }
            .low-stress {
              color: #10b981;
            }
            .footer {
              text-align: center;
              color: #64748b;
              font-size: 12px;
              margin-top: 50px;
              border-top: 1px solid #e2e8f0;
              padding-top: 20px;
            }
            .print-btn {
              display: block;
              margin: 20px auto;
              padding: 10px 20px;
              background: linear-gradient(90deg, #38bdf8, #818cf8);
              color: white;
              border: none;
              border-radius: 5px;
              cursor: pointer;
              font-weight: bold;
            }
          </style>
        </head>
        <body>
          <button class="print-btn" onclick="window.print()">Print Report</button>

          <div class="header">
            <h1>SleepSense AI</h1>
            <h2>Stress Analysis Report</h2>
          </div>

          <div class="section">
            <h3>User Information</h3>
            <p><strong>User:</strong> ${username || 'User'}</p>
            <p><strong>Report Generated:</strong> ${new Date().toLocaleString()}</p>
          </div>

          <div class="section">
            <h3>Sleep Data</h3>
            <p><strong>Sleep Hours:</strong> ${sleepHoursInput && sleepHoursInput.value ? sleepHoursInput.value : 'Not entered'}</p>
            <p><strong>Sleep Quality:</strong> ${sleepQualityInput && sleepQualityInput.value ? sleepQualityInput.value + '/10' : 'Not entered'}</p>
            <p><strong>Current Mood:</strong> ${currentMoodSelect ? currentMoodSelect.value : 'Not selected'}</p>
          </div>

          <div class="section">
            <h3>Stress Analysis</h3>
            ${(() => {
              const stressScore = stressLevel ? parseFloat(stressLevel.textContent) : 0;
              let stressLevelText = "Not calculated";
              let stressClass = "";

              if (!isNaN(stressScore)) {
                if (stressScore >= 7) {
                  stressLevelText = "High Stress";
                  stressClass = "high-stress";
                } else if (stressScore >= 4) {
                  stressLevelText = "Moderate Stress";
                  stressClass = "moderate-stress";
                } else {
                  stressLevelText = "Low Stress";
                  stressClass = "low-stress";
                }
              }

              return `<p><strong>Current Stress Level:</strong> <span class="stress-level ${stressClass}">${stressScore.toFixed(1)}/10 (${stressLevelText})</span></p>`;
            })()}
            <p>${stressDescription ? stressDescription.textContent : 'No stress description available.'}</p>
          </div>

          <div class="section">
            <h3>Health Tip</h3>
            <div class="health-tip">
              <p>💡 ${healthTip ? healthTip.textContent : 'No health tip available.'}</p>
            </div>
          </div>

          <div class="section">
            <h3>Historical Data</h3>
            <p><strong>Average Sleep Hours:</strong> ${avgSleepHours ? avgSleepHours.textContent : 'N/A'}</p>
            <p><strong>Average Sleep Quality:</strong> ${avgSleepQuality ? avgSleepQuality.textContent : 'N/A'}</p>
            <p><strong>Average Stress Level:</strong> ${avgStressLevel ? avgStressLevel.textContent : 'N/A'}</p>
          </div>

          <div class="footer">
            Generated by SleepSense AI - Your Personal Stress Management Assistant
          </div>
        </body>
        </html>
      `;

      // Write the content to the new window
      newWindow.document.open();
      newWindow.document.documentElement.innerHTML = htmlContent;
      newWindow.document.close();

      showToast("Report opened in new tab!");
    } catch (error) {
      console.error("Export to new tab error:", error);
      showToast("Failed to open report in new tab: " + error.message);
    }
  }
});
