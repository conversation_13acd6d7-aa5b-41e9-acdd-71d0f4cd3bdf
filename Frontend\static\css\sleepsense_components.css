/* Consolidated CSS for Human Stress Detection Project */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;600;700&family=Poppins:wght@300;400;600;700&display=swap');

:root {
    --primary-dark: #0f172a;
    --primary-medium: #1e293b;
    --primary-light: #334155;
    --accent-blue: #38bdf8;
    --accent-purple: #818cf8;
    --accent-pink: #c084fc;
    --text-light: #e2e8f0;
    --text-muted: #94a3b8;
    --glass-bg: rgba(15, 23, 42, 0.7);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: rgba(0, 0, 0, 0.3);
    --transition-normal: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    scroll-padding-top: 80px;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-medium), var(--primary-light));
    color: var(--text-light);
    line-height: 1.6;
    overflow-x: hidden;
    position: relative;
}

/* Background Effects */
.particles-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1;
    pointer-events: none;
}

.particle {
    position: absolute;
    border-radius: 50%;
    pointer-events: none;
}

.brain-waves {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.1;
    z-index: -1;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1000 1000"><path d="M0,500 Q250,400 500,500 T1000,500" stroke="white" fill="none" stroke-width="2" /></svg>') repeat-x;
    background-size: 100% 100%;
    animation: wave 15s linear infinite;
    pointer-events: none;
}

@keyframes wave {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 1000px 0;
    }
}

/* Navigation */
.navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 5%;
    position: sticky;
    top: 0;
    z-index: 1000;
    background: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid var(--glass-border);
}

.logo {
    display: flex;
    align-items: center;
}

.logo a {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--text-light);
    font-weight: 700;
    font-size: 1.5rem;
    font-family: 'Montserrat', sans-serif;
    transition: var(--transition-normal);
}

.logo a:hover {
    transform: scale(1.05);
}

.logo span {
    margin-left: 10px;
    background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple), var(--accent-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
    margin: 0;
    padding: 0;
}

.nav-list li a {
    color: var(--text-light);
    text-decoration: none;
    font-size: 1rem;
    font-weight: 500;
    position: relative;
    padding: 0.5rem 0;
    transition: var(--transition-normal);
}

.nav-list li a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
    transition: var(--transition-normal);
}

.nav-list li a:hover {
    color: var(--accent-blue);
}

.nav-list li a:hover::after {
    width: 100%;
}

/* Buttons */
.btn, .primary-btn {
    padding: 0.8rem 2rem;
    border: none;
    border-radius: 30px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    z-index: 1;
    color: white;
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
    box-shadow: 0 4px 15px rgba(56, 189, 248, 0.3);
}

.btn:hover, .primary-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 7px 20px rgba(56, 189, 248, 0.5);
}

/* Dashboard Specific Styles */
.dashboard-container {
    width: 90%;
    max-width: 1200px;
    margin: 2rem auto;
    padding: 2rem;
    background: rgba(15, 23, 42, 0.7);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
    border: 1px solid var(--glass-border);
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--glass-border);
}

.user-info {
    background: rgba(30, 41, 59, 0.7);
    padding: 1.5rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border: 1px solid var(--glass-border);
}

.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.dashboard-card {
    background: rgba(30, 41, 59, 0.7);
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border: 1px solid var(--glass-border);
}

.input-section {
    margin-bottom: 1.5rem;
}

.input-section label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.input-section input {
    width: 100%;
    padding: 0.8rem 1rem;
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    color: var(--text-light);
    font-family: 'Poppins', sans-serif;
}

/* Graph Container */
.graph-container {
    width: 100%;
    height: 400px;
    margin-top: 1.5rem;
    border-radius: 15px;
    overflow: hidden;
    background: rgba(15, 23, 42, 0.8);
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.3);
}

/* Chatbot Styles */
.chatbot-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.chatbot-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    margin-bottom: 1rem;
    background: rgba(15, 23, 42, 0.8);
    border-radius: 15px;
    max-height: 300px;
}

.chatbot-input {
    display: flex;
    gap: 1rem;
}

.chatbot-input input {
    flex: 1;
    padding: 0.8rem 1rem;
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    color: var(--text-light);
    font-family: 'Poppins', sans-serif;
}

.user-message, .bot-message, .chat-message {
    padding: 0.8rem 1rem;
    margin-bottom: 0.8rem;
    border-radius: 10px;
    max-width: 80%;
}

.user-message {
    background: var(--accent-blue);
    color: white;
    align-self: flex-end;
    margin-left: auto;
}

.bot-message, .chat-message {
    background: rgba(51, 65, 85, 0.8);
    color: var(--text-light);
    align-self: flex-start;
}

/* Footer Section */
.footer-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--glass-border);
}

#currentDateTime {
    color: var(--text-muted);
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .footer-section {
        flex-direction: column;
        gap: 1rem;
    }
}
